"""
Celery configuration for PDFlex project.

This module configures Celery for handling asynchronous tasks,
particularly for PDF generation and processing.
"""

import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pdflex.settings.development')

app = Celery('pdflex')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery configuration
app.conf.update(
    # Task routing
    task_routes={
        'apps.pdf_generation.tasks.generate_code_async': {'queue': 'pdf_generation'},
        'apps.pdf_generation.tasks.generate_preview_async': {'queue': 'pdf_preview'},
        'apps.pdf_generation.tasks.batch_generate_code': {'queue': 'pdf_batch'},
        'apps.pdf_generation.tasks.cleanup_expired_generated_code': {'queue': 'cleanup'},
        'apps.pdf_generation.tasks.cleanup_old_previews': {'queue': 'cleanup'},
    },
    
    # Task time limits
    task_time_limit=300,  # 5 minutes
    task_soft_time_limit=240,  # 4 minutes
    
    # Task retry configuration
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # Result backend configuration
    result_expires=3600,  # 1 hour
    
    # Worker configuration
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-expired-code': {
            'task': 'apps.pdf_generation.tasks.cleanup_expired_generated_code',
            'schedule': 3600.0,  # Every hour
        },
        'cleanup-old-previews': {
            'task': 'apps.pdf_generation.tasks.cleanup_old_previews',
            'schedule': 86400.0,  # Every day
        },
    },
    timezone='UTC',
)


@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery configuration."""
    print(f'Request: {self.request!r}')


# Custom task base class for PDF generation tasks
class PDFGenerationTask(app.Task):
    """Base task class for PDF generation tasks."""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure."""
        print(f'PDF Generation task {task_id} failed: {exc}')
        
        # Update database record if applicable
        if len(args) > 0:
            try:
                from apps.pdf_generation.models import GeneratedCode, TemplatePreview
                
                # Try to find and update the related record
                if 'generate_code' in self.name:
                    # This is a code generation task
                    template_id = args[0]
                    language = args[1] if len(args) > 1 else 'unknown'
                    
                    # Find the most recent generating code for this template/language
                    code = GeneratedCode.objects.filter(
                        template_id=template_id,
                        language=language,
                        status=GeneratedCode.Status.GENERATING
                    ).first()
                    
                    if code:
                        code.status = GeneratedCode.Status.FAILED
                        code.error_message = str(exc)
                        code.save()
                
                elif 'generate_preview' in self.name:
                    # This is a preview generation task
                    template_id = args[0]
                    
                    # Find the most recent generating preview for this template
                    preview = TemplatePreview.objects.filter(
                        template_id=template_id,
                        status=TemplatePreview.PreviewStatus.GENERATING
                    ).first()
                    
                    if preview:
                        preview.status = TemplatePreview.PreviewStatus.FAILED
                        preview.error_message = str(exc)
                        preview.save()
                        
            except Exception as e:
                print(f'Failed to update database record on task failure: {e}')
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success."""
        print(f'PDF Generation task {task_id} completed successfully')


# Register the custom task base class
app.Task = PDFGenerationTask
