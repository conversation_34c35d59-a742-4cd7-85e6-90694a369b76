"""
Production settings for PDFLEX project.
"""

from .base import *

# Security settings
DEBUG = False
SECRET_KEY = config('SECRET_KEY')  # Must be set in production

# Security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# HTTPS settings
SECURE_SSL_REDIRECT = config('SECURE_SSL_REDIRECT', default=True, cast=bool)
SECURE_HSTS_SECONDS = config('SECURE_HSTS_SECONDS', default=31536000, cast=int)
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Session security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Database connection pooling
DATABASES['default']['CONN_MAX_AGE'] = 60
DATABASES['default']['OPTIONS']['MAX_CONNS'] = 20

# Static files - use <PERSON>Noise or similar in production
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'

# Email configuration for production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST', default='')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')

# Logging for production
LOGGING['handlers']['file']['filename'] = '/var/log/pdflex/django.log'
LOGGING['handlers']['console']['level'] = 'WARNING'
LOGGING['loggers']['django']['level'] = 'WARNING'
LOGGING['loggers']['pdflex']['level'] = 'INFO'

# Add error logging
LOGGING['handlers']['error_file'] = {
    'level': 'ERROR',
    'class': 'logging.FileHandler',
    'filename': '/var/log/pdflex/django_errors.log',
    'formatter': 'verbose',
}

LOGGING['loggers']['django']['handlers'].append('error_file')
LOGGING['loggers']['pdflex']['handlers'].append('error_file')

# Cache configuration - ensure Redis is available
CACHES['default']['OPTIONS']['CONNECTION_POOL_KWARGS'] = {
    'max_connections': 50,
    'retry_on_timeout': True,
}

# CORS - restrict in production
CORS_ALLOW_ALL_ORIGINS = False
CORS_ALLOW_CREDENTIALS = True

# Performance settings
USE_ETAGS = True
PREPEND_WWW = config('PREPEND_WWW', default=False, cast=bool)

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB

# Admin security
ADMIN_URL = config('ADMIN_URL', default='admin/')

# Celery production settings
CELERY_TASK_ALWAYS_EAGER = False
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True

# Celery broker and result backend
CELERY_BROKER_URL = config('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')

# Celery security
CELERY_BROKER_USE_SSL = config('CELERY_BROKER_USE_SSL', default=False, cast=bool)
CELERY_REDIS_BACKEND_USE_SSL = config('CELERY_REDIS_BACKEND_USE_SSL', default=False, cast=bool)

# Celery monitoring
CELERY_SEND_TASK_EVENTS = True
CELERY_TASK_SEND_SENT_EVENT = True

# PDF Generation production settings
PDF_GENERATION_MAX_FILE_SIZE = 52428800  # 50 MB in production
PDF_CODE_MAX_DOWNLOADS = 1000  # Higher limit in production
PDF_TEMPLATE_MAX_ELEMENTS = 200  # Higher limit in production

# Security for file uploads
SECURE_FILE_UPLOAD_PERMISSIONS = 0o644
SECURE_FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755
