"""
Development settings for PDFLEX project.
"""

from .base import *

# Debug settings
DEBUG = True

# Database - Allow fallback to SQLite for development
if not config('DB_NAME', default=''):
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

# Development-specific apps
INSTALLED_APPS += [
    'debug_toolbar',
]

# Remove django_ratelimit from installed apps in development
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != 'django_ratelimit']

# Development middleware
MIDDLEWARE = [
    'debug_toolbar.middleware.DebugToolbarMiddleware',
] + MIDDLEWARE

# Debug toolbar configuration
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Cache - Use locmem cache for development if Redis is not available
if not config('REDIS_URL', default=''):
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }

# Disable rate limiting in development
RATELIMIT_ENABLE = False

# Disable CSRF for API development (be careful with this)
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# Development logging - more verbose
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['pdflex']['level'] = 'DEBUG'

# Static files - use Django's development server
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Development-specific settings
ALLOWED_HOSTS = ['*']  # Allow all hosts in development

# Celery - Use eager execution in development for testing
CELERY_TASK_ALWAYS_EAGER = config('CELERY_EAGER', default=True, cast=bool)
CELERY_TASK_EAGER_PROPAGATES = True

# Celery broker - Use Redis if available, otherwise use in-memory
CELERY_BROKER_URL = config('CELERY_BROKER_URL', default='memory://')
CELERY_RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default='cache+locmem://')

# PDF Generation development settings
PDF_GENERATION_CACHE_TIMEOUT = 300  # 5 minutes for development
PDF_CODE_EXPIRY_DAYS = 7  # Shorter expiry in development
PDF_PREVIEW_EXPIRY_DAYS = 3  # Shorter expiry in development
