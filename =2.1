Collecting Jinja2
  Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━ 134.9/134.9 kB 703.4 kB/s eta 0:00:00
Collecting MarkupSafe
  Downloading MarkupSafe-3.0.2-cp310-cp310-macosx_10_9_universal2.whl (14 kB)
Collecting python-slugify
  Downloading python_slugify-8.0.4-py2.py3-none-any.whl (10 kB)
Collecting jsonschema
  Downloading jsonschema-4.24.0-py3-none-any.whl (88 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 88.7/88.7 kB 3.1 MB/s eta 0:00:00
Collecting text-unidecode>=1.3
  Downloading text_unidecode-1.3-py2.py3-none-any.whl (78 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.2/78.2 kB 3.1 MB/s eta 0:00:00
Collecting rpds-py>=0.7.1
  Downloading rpds_py-0.26.0-cp310-cp310-macosx_10_12_x86_64.whl (372 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━ 372.5/372.5 kB 2.9 MB/s eta 0:00:00
Collecting attrs>=22.2.0
  Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 3.3 MB/s eta 0:00:00
Collecting referencing>=0.28.4
  Downloading referencing-0.36.2-py3-none-any.whl (26 kB)
Collecting jsonschema-specifications>=2023.03.6
  Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)
Requirement already satisfied: typing-extensions>=4.4.0 in ./env/lib/python3.10/site-packages (from referencing>=0.28.4->jsonschema) (4.14.1)
Installing collected packages: text-unidecode, rpds-py, python-slugify, MarkupSafe, attrs, referencing, Jinja2, jsonschema-specifications, jsonschema
Successfully installed Jinja2-3.1.6 MarkupSafe-3.0.2 attrs-25.3.0 jsonschema-4.24.0 jsonschema-specifications-2025.4.1 python-slugify-8.0.4 referencing-0.36.2 rpds-py-0.26.0 text-unidecode-1.3
