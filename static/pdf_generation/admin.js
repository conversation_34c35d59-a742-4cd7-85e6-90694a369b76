/* Custom JavaScript for PDF Generation Admin Interface */

(function($) {
    'use strict';

    // Initialize when DOM is ready
    $(document).ready(function() {
        initializeTemplateAdmin();
        initializeElementAdmin();
        initializeCodeGeneration();
        initializePreviewGeneration();
    });

    function initializeTemplateAdmin() {
        // Auto-generate slug from name
        $('#id_name').on('input', function() {
            const name = $(this).val();
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            $('#id_slug').val(slug);
        });

        // Page size change handler
        $('#id_page_size').on('change', function() {
            const isCustom = $(this).val() === 'CUSTOM';
            $('.field-custom_width, .field-custom_height').toggle(isCustom);
        });

        // Template inheritance warning
        $('#id_parent_template').on('change', function() {
            if ($(this).val()) {
                showWarning('Template inheritance is enabled. Child template will inherit settings from parent.');
            }
        });

        // Usage statistics formatting
        formatUsageStats();
    }

    function initializeElementAdmin() {
        // Element type change handler
        $('.field-element_type select').on('change', function() {
            const elementType = $(this).val();
            updateElementTypeIcon($(this), elementType);
        });

        // Position validation
        $('.field-x input, .field-y input').on('input', function() {
            validatePosition($(this));
        });

        // Size validation
        $('.field-width input, .field-height input').on('input', function() {
            validateSize($(this));
        });

        // Initialize element type icons
        $('.field-element_type select').each(function() {
            updateElementTypeIcon($(this), $(this).val());
        });
    }

    function initializeCodeGeneration() {
        // Add generate code buttons to template admin
        if (window.location.pathname.includes('/pdftemplate/')) {
            addGenerateCodeButtons();
        }

        // Handle code generation clicks
        $(document).on('click', '.generate-code-btn', function(e) {
            e.preventDefault();
            const templateId = $(this).data('template-id');
            const language = $(this).data('language');
            generateCode(templateId, language);
        });
    }

    function initializePreviewGeneration() {
        // Add preview button to template admin
        if (window.location.pathname.includes('/pdftemplate/')) {
            addPreviewButton();
        }

        // Handle preview generation clicks
        $(document).on('click', '.generate-preview-btn', function(e) {
            e.preventDefault();
            const templateId = $(this).data('template-id');
            generatePreview(templateId);
        });
    }

    function updateElementTypeIcon(selectElement, elementType) {
        const iconMap = {
            'text': '📝',
            'image': '🖼️',
            'table': '📊',
            'container': '📦'
        };

        const icon = iconMap[elementType] || '❓';
        selectElement.closest('tr').find('td:first').prepend(`<span class="element-icon">${icon}</span> `);
    }

    function validatePosition(input) {
        const value = parseFloat(input.val());
        if (isNaN(value) || value < 0) {
            input.addClass('error');
            showError('Position values must be non-negative numbers');
        } else {
            input.removeClass('error');
        }
    }

    function validateSize(input) {
        const value = parseFloat(input.val());
        if (value !== '' && (isNaN(value) || value <= 0)) {
            input.addClass('error');
            showError('Size values must be positive numbers');
        } else {
            input.removeClass('error');
        }
    }

    function formatUsageStats() {
        $('.field-usage_count, .field-download_count').each(function() {
            const value = parseInt($(this).text());
            if (value > 0) {
                $(this).addClass('has-usage');
            }
        });

        $('.field-generation_time').each(function() {
            const time = parseFloat($(this).text());
            if (time < 1) {
                $(this).addClass('generation-time fast');
            } else if (time < 5) {
                $(this).addClass('generation-time medium');
            } else {
                $(this).addClass('generation-time slow');
            }
        });
    }

    function addGenerateCodeButtons() {
        const templateId = getTemplateIdFromUrl();
        if (!templateId) return;

        const languages = ['python', 'javascript', 'php'];
        const buttonContainer = $('<div class="generate-code-container"></div>');

        languages.forEach(lang => {
            const button = $(`
                <button type="button" class="button generate-code-btn" 
                        data-template-id="${templateId}" data-language="${lang}">
                    Generate ${lang.charAt(0).toUpperCase() + lang.slice(1)} Code
                </button>
            `);
            buttonContainer.append(button);
        });

        $('.submit-row').before(buttonContainer);
    }

    function addPreviewButton() {
        const templateId = getTemplateIdFromUrl();
        if (!templateId) return;

        const button = $(`
            <button type="button" class="button generate-preview-btn" 
                    data-template-id="${templateId}">
                Generate Preview
            </button>
        `);

        $('.submit-row').before(button);
    }

    function generateCode(templateId, language) {
        const button = $(`.generate-code-btn[data-template-id="${templateId}"][data-language="${language}"]`);
        const originalText = button.text();
        
        button.prop('disabled', true).text('Generating...');

        // Get CSRF token
        const csrfToken = $('[name=csrfmiddlewaretoken]').val();

        $.ajax({
            url: `/pdf-generation/api/template/${templateId}/generate-code/`,
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                language: language,
                options: {}
            }),
            success: function(response) {
                if (response.success) {
                    showSuccess(`${language} code generation started successfully!`);
                    // Poll for completion (simplified)
                    setTimeout(() => {
                        button.prop('disabled', false).text(originalText);
                        showSuccess(`${language} code generation completed!`);
                    }, 3000);
                } else {
                    showError(`Failed to start ${language} code generation: ${response.error}`);
                    button.prop('disabled', false).text(originalText);
                }
            },
            error: function(xhr, status, error) {
                showError(`Error generating ${language} code: ${error}`);
                button.prop('disabled', false).text(originalText);
            }
        });
    }

    function generatePreview(templateId) {
        const button = $(`.generate-preview-btn[data-template-id="${templateId}"]`);
        const originalText = button.text();
        
        button.prop('disabled', true).text('Generating Preview...');

        // Get CSRF token
        const csrfToken = $('[name=csrfmiddlewaretoken]').val();

        $.ajax({
            url: `/pdf-generation/api/template/${templateId}/generate-preview/`,
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                sample_data: getDefaultSampleData()
            }),
            success: function(response) {
                if (response.success) {
                    showSuccess('Preview generation started successfully!');
                    // Poll for completion (simplified)
                    setTimeout(() => {
                        button.prop('disabled', false).text(originalText);
                        showSuccess('Preview generation completed!');
                    }, 3000);
                } else {
                    showError(`Failed to start preview generation: ${response.error}`);
                    button.prop('disabled', false).text(originalText);
                }
            },
            error: function(xhr, status, error) {
                showError(`Error generating preview: ${error}`);
                button.prop('disabled', false).text(originalText);
            }
        });
    }

    function getTemplateIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        const templateIndex = pathParts.findIndex(part => part === 'pdftemplate');
        if (templateIndex !== -1 && pathParts[templateIndex + 1]) {
            return pathParts[templateIndex + 1];
        }
        return null;
    }

    function getDefaultSampleData() {
        return {
            title: 'Sample Document',
            date: new Date().toISOString().split('T')[0],
            content: 'This is sample content for preview generation.',
            items: [
                { name: 'Sample Item 1', value: 100 },
                { name: 'Sample Item 2', value: 200 }
            ],
            total: 300,
            customer: {
                name: 'John Doe',
                email: '<EMAIL>'
            }
        };
    }

    function showSuccess(message) {
        const messageDiv = $(`
            <div class="messagelist">
                <div class="success">${message}</div>
            </div>
        `);
        $('.content').prepend(messageDiv);
        setTimeout(() => messageDiv.fadeOut(), 5000);
    }

    function showError(message) {
        const messageDiv = $(`
            <div class="messagelist">
                <div class="error">${message}</div>
            </div>
        `);
        $('.content').prepend(messageDiv);
        setTimeout(() => messageDiv.fadeOut(), 8000);
    }

    function showWarning(message) {
        const messageDiv = $(`
            <div class="messagelist">
                <div class="warning">${message}</div>
            </div>
        `);
        $('.content').prepend(messageDiv);
        setTimeout(() => messageDiv.fadeOut(), 6000);
    }

    // Global function for template list generate code
    window.generateCode = function(templateId, language) {
        generateCode(templateId, language);
    };

})(django.jQuery);
