/* Custom CSS for PDF Generation Admin Interface */

/* Template preview styling */
.template-preview {
    max-width: 200px;
    max-height: 150px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-active { background-color: #d4edda; color: #155724; }
.status-draft { background-color: #fff3cd; color: #856404; }
.status-archived { background-color: #f8d7da; color: #721c24; }
.status-completed { background-color: #d1ecf1; color: #0c5460; }
.status-failed { background-color: #f8d7da; color: #721c24; }
.status-generating { background-color: #fff3cd; color: #856404; }

/* Element type icons */
.element-type-text::before { content: "📝"; margin-right: 5px; }
.element-type-image::before { content: "🖼️"; margin-right: 5px; }
.element-type-table::before { content: "📊"; margin-right: 5px; }
.element-type-container::before { content: "📦"; margin-right: 5px; }

/* Language icons */
.language-python::before { content: "🐍"; margin-right: 5px; }
.language-javascript::before { content: "🟨"; margin-right: 5px; }
.language-php::before { content: "🐘"; margin-right: 5px; }

/* Admin form improvements */
.field-preview_link, .field-generate_code_links, .field-download_link, .field-preview_files {
    font-weight: normal;
}

.field-preview_link a, .field-generate_code_links a, .field-download_link a, .field-preview_files a {
    color: #007cba;
    text-decoration: none;
    padding: 2px 6px;
    border: 1px solid #007cba;
    border-radius: 3px;
    font-size: 11px;
}

.field-preview_link a:hover, .field-generate_code_links a:hover, .field-download_link a:hover, .field-preview_files a:hover {
    background-color: #007cba;
    color: white;
}

/* JSON field styling */
.field-metadata textarea, .field-settings textarea, .field-sample_data textarea,
.field-template_data textarea, .field-elements_data textarea, .field-framework_options textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* Inline element styling */
.inline-group .tabular tr.has_original td {
    padding: 8px 10px;
}

.inline-group .tabular .element_type select {
    min-width: 100px;
}

/* Template hierarchy visualization */
.template-hierarchy {
    margin-left: 20px;
    border-left: 2px solid #ddd;
    padding-left: 10px;
}

/* Usage statistics */
.usage-stats {
    display: flex;
    gap: 15px;
    margin: 10px 0;
}

.usage-stat {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    min-width: 80px;
}

.usage-stat .number {
    font-size: 18px;
    font-weight: bold;
    color: #007cba;
}

.usage-stat .label {
    font-size: 11px;
    color: #666;
    text-transform: uppercase;
}

/* Template element positioning helper */
.position-helper {
    display: inline-block;
    font-family: monospace;
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 11px;
}

/* File size formatting */
.file-size {
    font-size: 11px;
    color: #666;
    font-style: italic;
}

/* Generation time formatting */
.generation-time {
    font-size: 11px;
    color: #666;
}

.generation-time.fast { color: #28a745; }
.generation-time.medium { color: #ffc107; }
.generation-time.slow { color: #dc3545; }

/* Error message styling */
.error-message {
    color: #dc3545;
    font-size: 11px;
    font-style: italic;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Template version timeline */
.version-timeline {
    position: relative;
    padding-left: 20px;
}

.version-timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.version-item {
    position: relative;
    margin-bottom: 15px;
}

.version-item::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #007cba;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #007cba;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .usage-stats {
        flex-direction: column;
    }
    
    .field-generate_code_links a {
        display: block;
        margin: 2px 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .status-badge {
        filter: brightness(0.8);
    }
    
    .usage-stat {
        background: #2b2b2b;
        color: #fff;
    }
    
    .position-helper {
        background: #3a3a3a;
        color: #fff;
    }
}
