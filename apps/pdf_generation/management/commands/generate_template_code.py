"""
Management command for generating code from PDF templates.

This command allows bulk code generation for templates from the command line.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.pdf_generation.models import PDFTemplate, GeneratedCode
from apps.pdf_generation.services import PDFGenerationService
from apps.pdf_generation.engines import get_supported_languages
import json


class Command(BaseCommand):
    help = 'Generate code for PDF templates'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--template-id',
            type=str,
            help='UUID of specific template to generate code for'
        )
        
        parser.add_argument(
            '--template-slug',
            type=str,
            help='Slug of specific template to generate code for'
        )
        
        parser.add_argument(
            '--language',
            type=str,
            required=True,
            choices=get_supported_languages(),
            help='Target programming language'
        )
        
        parser.add_argument(
            '--all-templates',
            action='store_true',
            help='Generate code for all active templates'
        )
        
        parser.add_argument(
            '--options',
            type=str,
            help='JSON string of generation options'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration even if code already exists'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be generated without actually generating'
        )
    
    def handle(self, *args, **options):
        # Parse options
        generation_options = {}
        if options['options']:
            try:
                generation_options = json.loads(options['options'])
            except json.JSONDecodeError:
                raise CommandError('Invalid JSON in --options parameter')
        
        # Get templates to process
        templates = self.get_templates(options)
        
        if not templates:
            self.stdout.write(
                self.style.WARNING('No templates found matching criteria')
            )
            return
        
        self.stdout.write(
            f"Found {len(templates)} template(s) to process for language: {options['language']}"
        )
        
        if options['dry_run']:
            self.show_dry_run(templates, options['language'], generation_options)
            return
        
        # Generate code for templates
        service = PDFGenerationService()
        successful = 0
        failed = 0
        
        for template in templates:
            try:
                # Check if code already exists
                if not options['force']:
                    existing = GeneratedCode.objects.filter(
                        template=template,
                        language=options['language'],
                        status=GeneratedCode.Status.COMPLETED
                    ).first()
                    
                    if existing:
                        self.stdout.write(
                            f"Skipping {template.name} - code already exists (use --force to regenerate)"
                        )
                        continue
                
                self.stdout.write(f"Generating code for: {template.name}")
                
                with transaction.atomic():
                    generated_code = service.generate_code(
                        template, 
                        options['language'], 
                        generation_options
                    )
                
                if generated_code.status == GeneratedCode.Status.COMPLETED:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✓ Successfully generated code for {template.name} "
                            f"(ID: {generated_code.id})"
                        )
                    )
                    successful += 1
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f"✗ Failed to generate code for {template.name}: "
                            f"{generated_code.error_message}"
                        )
                    )
                    failed += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"✗ Error processing {template.name}: {e}")
                )
                failed += 1
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write(f"Generation Summary:")
        self.stdout.write(f"  Successful: {successful}")
        self.stdout.write(f"  Failed: {failed}")
        self.stdout.write(f"  Total: {successful + failed}")
        
        if failed > 0:
            self.stdout.write(
                self.style.WARNING(f"\n{failed} template(s) failed to generate code")
            )
    
    def get_templates(self, options):
        """Get templates based on command options."""
        if options['template_id']:
            try:
                return [PDFTemplate.objects.get(id=options['template_id'])]
            except PDFTemplate.DoesNotExist:
                raise CommandError(f"Template with ID {options['template_id']} not found")
        
        elif options['template_slug']:
            try:
                return [PDFTemplate.objects.get(slug=options['template_slug'])]
            except PDFTemplate.DoesNotExist:
                raise CommandError(f"Template with slug '{options['template_slug']}' not found")
        
        elif options['all_templates']:
            return PDFTemplate.objects.filter(status=PDFTemplate.Status.ACTIVE)
        
        else:
            raise CommandError(
                "Must specify --template-id, --template-slug, or --all-templates"
            )
    
    def show_dry_run(self, templates, language, options):
        """Show what would be generated in dry run mode."""
        self.stdout.write(self.style.WARNING("DRY RUN MODE - No code will be generated"))
        self.stdout.write(f"Language: {language}")
        self.stdout.write(f"Options: {json.dumps(options, indent=2) if options else 'None'}")
        self.stdout.write("\nTemplates to process:")
        
        for template in templates:
            self.stdout.write(f"  - {template.name} (ID: {template.id})")
            self.stdout.write(f"    Status: {template.get_status_display()}")
            self.stdout.write(f"    Elements: {template.elements.count()}")
            
            # Check existing code
            existing = GeneratedCode.objects.filter(
                template=template,
                language=language,
                status=GeneratedCode.Status.COMPLETED
            ).first()
            
            if existing:
                self.stdout.write(f"    Existing code: Yes (created {existing.created_at})")
            else:
                self.stdout.write(f"    Existing code: No")
            
            self.stdout.write("")
