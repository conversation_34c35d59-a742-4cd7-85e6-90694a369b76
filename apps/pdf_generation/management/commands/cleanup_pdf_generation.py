"""
Management command for cleaning up PDF generation data.

This command helps maintain the system by cleaning up old files,
expired data, and optimizing storage usage.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Sum
from apps.pdf_generation.models import GeneratedCode, TemplatePreview, TemplateVersion
from apps.pdf_generation.tasks import cleanup_expired_generated_code, cleanup_old_previews
import os


class Command(BaseCommand):
    help = 'Clean up PDF generation data and files'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--expired-code',
            action='store_true',
            help='Clean up expired generated code packages'
        )
        
        parser.add_argument(
            '--old-previews',
            action='store_true',
            help='Clean up old template previews (older than 30 days)'
        )
        
        parser.add_argument(
            '--failed-generations',
            action='store_true',
            help='Clean up failed code generations (older than 7 days)'
        )
        
        parser.add_argument(
            '--orphaned-files',
            action='store_true',
            help='Clean up orphaned files without database records'
        )
        
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all cleanup operations'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned without actually cleaning'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show storage statistics'
        )
    
    def handle(self, *args, **options):
        if options['stats']:
            self.show_statistics()
            return
        
        if not any([
            options['expired_code'],
            options['old_previews'], 
            options['failed_generations'],
            options['orphaned_files'],
            options['all']
        ]):
            self.stdout.write(
                self.style.WARNING(
                    'No cleanup operations specified. Use --help to see available options.'
                )
            )
            return
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will be deleted')
            )
        
        total_cleaned = 0
        total_size_freed = 0
        
        # Clean expired code
        if options['expired_code'] or options['all']:
            cleaned, size_freed = self.cleanup_expired_code(options['dry_run'])
            total_cleaned += cleaned
            total_size_freed += size_freed
        
        # Clean old previews
        if options['old_previews'] or options['all']:
            cleaned, size_freed = self.cleanup_old_previews(options['dry_run'])
            total_cleaned += cleaned
            total_size_freed += size_freed
        
        # Clean failed generations
        if options['failed_generations'] or options['all']:
            cleaned, size_freed = self.cleanup_failed_generations(options['dry_run'])
            total_cleaned += cleaned
            total_size_freed += size_freed
        
        # Clean orphaned files
        if options['orphaned_files'] or options['all']:
            cleaned, size_freed = self.cleanup_orphaned_files(options['dry_run'])
            total_cleaned += cleaned
            total_size_freed += size_freed
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write("Cleanup Summary:")
        self.stdout.write(f"  Items cleaned: {total_cleaned}")
        self.stdout.write(f"  Storage freed: {self.format_size(total_size_freed)}")
        
        if not options['dry_run'] and total_cleaned > 0:
            self.stdout.write(
                self.style.SUCCESS(f"\n✓ Cleanup completed successfully")
            )
    
    def cleanup_expired_code(self, dry_run=False):
        """Clean up expired generated code packages."""
        self.stdout.write("Cleaning expired generated code...")
        
        expired_codes = GeneratedCode.objects.filter(
            expires_at__lt=timezone.now()
        ).exclude(expires_at__isnull=True)
        
        count = expired_codes.count()
        size_freed = 0
        
        if dry_run:
            for code in expired_codes:
                if code.code_package:
                    try:
                        size_freed += code.code_package.size
                    except:
                        pass
            
            self.stdout.write(f"  Would clean {count} expired code packages")
            self.stdout.write(f"  Would free {self.format_size(size_freed)}")
        else:
            for code in expired_codes:
                if code.code_package:
                    try:
                        size_freed += code.code_package.size
                        code.code_package.delete()
                    except:
                        pass
                code.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f"  ✓ Cleaned {count} expired code packages")
            )
        
        return count, size_freed
    
    def cleanup_old_previews(self, dry_run=False):
        """Clean up old template previews."""
        self.stdout.write("Cleaning old template previews...")
        
        cutoff_date = timezone.now() - timezone.timedelta(days=30)
        old_previews = TemplatePreview.objects.filter(created_at__lt=cutoff_date)
        
        count = old_previews.count()
        size_freed = 0
        
        if dry_run:
            for preview in old_previews:
                if preview.preview_pdf:
                    try:
                        size_freed += preview.preview_pdf.size
                    except:
                        pass
                if preview.preview_image:
                    try:
                        size_freed += preview.preview_image.size
                    except:
                        pass
            
            self.stdout.write(f"  Would clean {count} old previews")
            self.stdout.write(f"  Would free {self.format_size(size_freed)}")
        else:
            for preview in old_previews:
                if preview.preview_pdf:
                    try:
                        size_freed += preview.preview_pdf.size
                        preview.preview_pdf.delete()
                    except:
                        pass
                if preview.preview_image:
                    try:
                        size_freed += preview.preview_image.size
                        preview.preview_image.delete()
                    except:
                        pass
                preview.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f"  ✓ Cleaned {count} old previews")
            )
        
        return count, size_freed
    
    def cleanup_failed_generations(self, dry_run=False):
        """Clean up failed code generations."""
        self.stdout.write("Cleaning failed code generations...")
        
        cutoff_date = timezone.now() - timezone.timedelta(days=7)
        failed_codes = GeneratedCode.objects.filter(
            status=GeneratedCode.Status.FAILED,
            created_at__lt=cutoff_date
        )
        
        count = failed_codes.count()
        size_freed = 0
        
        if dry_run:
            self.stdout.write(f"  Would clean {count} failed generations")
        else:
            failed_codes.delete()
            self.stdout.write(
                self.style.SUCCESS(f"  ✓ Cleaned {count} failed generations")
            )
        
        return count, size_freed
    
    def cleanup_orphaned_files(self, dry_run=False):
        """Clean up orphaned files without database records."""
        self.stdout.write("Cleaning orphaned files...")
        
        # This is a simplified implementation
        # In production, you'd want to scan the media directories
        # and check for files without corresponding database records
        
        count = 0
        size_freed = 0
        
        if dry_run:
            self.stdout.write("  Would scan for orphaned files")
        else:
            self.stdout.write("  Orphaned file cleanup not implemented yet")
        
        return count, size_freed
    
    def show_statistics(self):
        """Show storage and usage statistics."""
        self.stdout.write(self.style.SUCCESS("PDF Generation Storage Statistics"))
        self.stdout.write("="*50)
        
        # Generated code statistics
        code_stats = GeneratedCode.objects.aggregate(
            total=Count('id'),
            completed=Count('id', filter=models.Q(status=GeneratedCode.Status.COMPLETED)),
            failed=Count('id', filter=models.Q(status=GeneratedCode.Status.FAILED)),
            pending=Count('id', filter=models.Q(status=GeneratedCode.Status.PENDING))
        )
        
        self.stdout.write("Generated Code:")
        self.stdout.write(f"  Total: {code_stats['total']}")
        self.stdout.write(f"  Completed: {code_stats['completed']}")
        self.stdout.write(f"  Failed: {code_stats['failed']}")
        self.stdout.write(f"  Pending: {code_stats['pending']}")
        
        # Preview statistics
        preview_stats = TemplatePreview.objects.aggregate(
            total=Count('id'),
            completed=Count('id', filter=models.Q(status=TemplatePreview.PreviewStatus.COMPLETED)),
            failed=Count('id', filter=models.Q(status=TemplatePreview.PreviewStatus.FAILED))
        )
        
        self.stdout.write("\nTemplate Previews:")
        self.stdout.write(f"  Total: {preview_stats['total']}")
        self.stdout.write(f"  Completed: {preview_stats['completed']}")
        self.stdout.write(f"  Failed: {preview_stats['failed']}")
        
        # Version statistics
        version_count = TemplateVersion.objects.count()
        self.stdout.write(f"\nTemplate Versions: {version_count}")
        
        # Expired data
        expired_code = GeneratedCode.objects.filter(
            expires_at__lt=timezone.now()
        ).exclude(expires_at__isnull=True).count()
        
        old_previews = TemplatePreview.objects.filter(
            created_at__lt=timezone.now() - timezone.timedelta(days=30)
        ).count()
        
        if expired_code > 0 or old_previews > 0:
            self.stdout.write("\nCleanup Opportunities:")
            if expired_code > 0:
                self.stdout.write(f"  Expired code packages: {expired_code}")
            if old_previews > 0:
                self.stdout.write(f"  Old previews (>30 days): {old_previews}")
    
    def format_size(self, size_bytes):
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
