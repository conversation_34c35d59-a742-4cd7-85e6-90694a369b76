"""
Django signals for PDF generation app.

This module handles automatic actions triggered by model changes,
such as version creation, cache invalidation, and cleanup tasks.
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.core.cache import cache
from django.utils import timezone

from .models import PDFTemplate, PDFElement, GeneratedCode, TemplatePreview
from .services import PDFGenerationService

logger = logging.getLogger(__name__)


@receiver(post_save, sender=PDFTemplate)
def create_template_version(sender, instance, created, **kwargs):
    """
    Create a template version when a template is saved.
    """
    try:
        # Skip version creation during initial migration or if explicitly disabled
        if getattr(instance, '_skip_version_creation', False):
            return

        # Skip if this is just a usage count update
        if hasattr(instance, '_mark_as_used') and instance._mark_as_used:
            return

        # Determine change type
        if created:
            change_type = 'created'
            change_description = 'Template created'
        else:
            change_type = 'updated'
            change_description = 'Template updated'

        # Check if version already exists for this template and version number
        from .models import TemplateVersion
        existing_version = TemplateVersion.objects.filter(
            template=instance,
            version_number=instance.version
        ).first()

        if existing_version:
            logger.debug(f"Version {instance.version} already exists for template {instance.id}")
            return

        # Create version using the service
        service = PDFGenerationService()
        service.create_template_version(
            template=instance,
            change_type=change_type,
            change_description=change_description
        )

        logger.info(f"Version created for template {instance.id}: {change_type}")

    except Exception as e:
        logger.error(f"Failed to create version for template {instance.id}: {e}")


@receiver(post_save, sender=PDFElement)
def invalidate_template_cache(sender, instance, **kwargs):
    """
    Invalidate template-related caches when elements are modified.
    """
    try:
        template = instance.template
        
        # Invalidate template compilation cache
        cache_keys = [
            f"template_compiled_{template.id}",
            f"template_validation_{template.id}",
            f"template_elements_{template.id}",
        ]
        
        cache.delete_many(cache_keys)
        
        # Update template's updated_at timestamp
        template.updated_at = timezone.now()
        template._skip_version_creation = True  # Prevent recursive version creation
        template.save(update_fields=['updated_at'])
        
        logger.debug(f"Cache invalidated for template {template.id} due to element change")
        
    except Exception as e:
        logger.error(f"Failed to invalidate cache for element {instance.id}: {e}")


@receiver(post_delete, sender=PDFElement)
def cleanup_element_cache(sender, instance, **kwargs):
    """
    Clean up caches when elements are deleted.
    """
    try:
        template = instance.template
        
        # Invalidate template caches
        cache_keys = [
            f"template_compiled_{template.id}",
            f"template_validation_{template.id}",
            f"template_elements_{template.id}",
        ]
        
        cache.delete_many(cache_keys)
        
        logger.debug(f"Cache cleaned up for deleted element {instance.id}")
        
    except Exception as e:
        logger.error(f"Failed to cleanup cache for deleted element {instance.id}: {e}")


@receiver(post_save, sender=GeneratedCode)
def handle_code_generation_completion(sender, instance, created, **kwargs):
    """
    Handle actions when code generation is completed or fails.
    """
    try:
        if not created:  # Only handle updates, not creation
            if instance.status == GeneratedCode.Status.COMPLETED:
                # Set expiry date if not set (default: 30 days)
                if not instance.expires_at:
                    instance.expires_at = timezone.now() + timezone.timedelta(days=30)
                    instance.save(update_fields=['expires_at'])
                
                logger.info(f"Code generation completed for template {instance.template.id}")
                
            elif instance.status == GeneratedCode.Status.FAILED:
                logger.warning(f"Code generation failed for template {instance.template.id}: {instance.error_message}")
        
    except Exception as e:
        logger.error(f"Failed to handle code generation completion for {instance.id}: {e}")


@receiver(post_delete, sender=GeneratedCode)
def cleanup_generated_files(sender, instance, **kwargs):
    """
    Clean up generated code files when GeneratedCode is deleted.
    """
    try:
        if instance.code_package:
            # Delete the actual file
            instance.code_package.delete(save=False)
            logger.debug(f"Cleaned up code package file for {instance.id}")
        
    except Exception as e:
        logger.error(f"Failed to cleanup files for deleted GeneratedCode {instance.id}: {e}")


@receiver(post_delete, sender=TemplatePreview)
def cleanup_preview_files(sender, instance, **kwargs):
    """
    Clean up preview files when TemplatePreview is deleted.
    """
    try:
        files_deleted = []
        
        if instance.preview_pdf:
            instance.preview_pdf.delete(save=False)
            files_deleted.append('PDF')
        
        if instance.preview_image:
            instance.preview_image.delete(save=False)
            files_deleted.append('Image')
        
        if files_deleted:
            logger.debug(f"Cleaned up preview files ({', '.join(files_deleted)}) for {instance.id}")
        
    except Exception as e:
        logger.error(f"Failed to cleanup files for deleted TemplatePreview {instance.id}: {e}")


@receiver(pre_save, sender=PDFTemplate)
def validate_template_before_save(sender, instance, **kwargs):
    """
    Validate template configuration before saving.
    """
    try:
        # Validate custom page dimensions
        if instance.page_size == PDFTemplate.PageSize.CUSTOM:
            if not instance.custom_width or not instance.custom_height:
                raise ValueError("Custom width and height are required for custom page size")
            
            if instance.custom_width < 50 or instance.custom_height < 50:
                raise ValueError("Custom dimensions must be at least 50mm")
            
            if instance.custom_width > 2000 or instance.custom_height > 2000:
                raise ValueError("Custom dimensions cannot exceed 2000mm")
        
        # Validate margins
        margins = [instance.margin_top, instance.margin_bottom, instance.margin_left, instance.margin_right]
        if any(margin < 0 for margin in margins):
            raise ValueError("Margins cannot be negative")
        
        if any(margin > 100 for margin in margins):
            raise ValueError("Margins cannot exceed 100mm")
        
        # Validate template inheritance (prevent circular references)
        if instance.parent_template:
            current = instance.parent_template
            visited = {instance.id} if instance.id else set()
            
            while current:
                if current.id in visited:
                    raise ValueError("Circular template inheritance detected")
                visited.add(current.id)
                current = current.parent_template
        
    except ValueError as e:
        logger.error(f"Template validation failed for {instance.id}: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during template validation for {instance.id}: {e}")


@receiver(post_save, sender=PDFTemplate)
def update_template_usage_stats(sender, instance, created, **kwargs):
    """
    Update template usage statistics and related metrics.
    """
    try:
        if not created:  # Only for updates
            # Update last_used timestamp if template was used for generation
            if hasattr(instance, '_mark_as_used') and instance._mark_as_used:
                instance.last_used = timezone.now()
                instance.usage_count += 1
                instance.save(update_fields=['last_used', 'usage_count'])
                delattr(instance, '_mark_as_used')  # Clean up the flag
        
    except Exception as e:
        logger.error(f"Failed to update usage stats for template {instance.id}: {e}")


# Signal to handle template status changes
@receiver(post_save, sender=PDFTemplate)
def handle_template_status_change(sender, instance, created, **kwargs):
    """
    Handle actions when template status changes.
    """
    try:
        if not created and hasattr(instance, '_original_status'):
            old_status = instance._original_status
            new_status = instance.status
            
            if old_status != new_status:
                if new_status == PDFTemplate.Status.ARCHIVED:
                    # Clean up related data for archived templates
                    logger.info(f"Template {instance.id} archived, consider cleanup")
                
                elif new_status == PDFTemplate.Status.ACTIVE and old_status == PDFTemplate.Status.DRAFT:
                    # Template published
                    logger.info(f"Template {instance.id} published")
        
    except Exception as e:
        logger.error(f"Failed to handle status change for template {instance.id}: {e}")


# Store original status for comparison
@receiver(pre_save, sender=PDFTemplate)
def store_original_status(sender, instance, **kwargs):
    """
    Store original status to detect changes.
    """
    try:
        if instance.pk:
            original = PDFTemplate.objects.get(pk=instance.pk)
            instance._original_status = original.status
    except PDFTemplate.DoesNotExist:
        instance._original_status = None
    except Exception as e:
        logger.error(f"Failed to store original status for template {instance.id}: {e}")


# Cleanup expired data periodically (this would typically be called by a management command)
def cleanup_expired_data():
    """
    Clean up expired generated code and old previews.
    This function can be called by management commands or scheduled tasks.
    """
    try:
        from .tasks import cleanup_expired_generated_code, cleanup_old_previews
        
        # Run cleanup tasks
        cleanup_expired_generated_code.delay()
        cleanup_old_previews.delay()
        
        logger.info("Scheduled cleanup tasks for expired data")
        
    except Exception as e:
        logger.error(f"Failed to schedule cleanup tasks: {e}")


# Custom signal for template compilation
from django.dispatch import Signal

template_compiled = Signal()
template_validation_failed = Signal()
code_generation_started = Signal()
code_generation_completed = Signal()
preview_generation_started = Signal()
preview_generation_completed = Signal()


@receiver(template_compiled)
def handle_template_compiled(sender, template, compilation_data, **kwargs):
    """
    Handle template compilation completion.
    """
    try:
        # Cache the compiled template
        cache_key = f"template_compiled_{template.id}"
        cache.set(cache_key, compilation_data, timeout=3600)  # Cache for 1 hour
        
        logger.debug(f"Template {template.id} compiled and cached")
        
    except Exception as e:
        logger.error(f"Failed to handle template compilation for {template.id}: {e}")


@receiver(code_generation_completed)
def handle_code_generation_completed(sender, generated_code, **kwargs):
    """
    Handle code generation completion.
    """
    try:
        # Update template usage
        template = generated_code.template
        template._mark_as_used = True
        template.save()
        
        logger.info(f"Code generation completed for template {template.id}, language: {generated_code.language}")
        
    except Exception as e:
        logger.error(f"Failed to handle code generation completion: {e}")


@receiver(preview_generation_completed)
def handle_preview_generation_completed(sender, preview, **kwargs):
    """
    Handle preview generation completion.
    """
    try:
        # Update template usage
        template = preview.template
        template._mark_as_used = True
        template.save()
        
        logger.info(f"Preview generation completed for template {template.id}")
        
    except Exception as e:
        logger.error(f"Failed to handle preview generation completion: {e}")
