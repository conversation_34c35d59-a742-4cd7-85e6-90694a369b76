"""
Celery tasks for PDF generation and processing.

This module provides asynchronous tasks for:
- Long-running code generation processes
- Batch template processing
- Asynchronous ZIP/repository creation
- Template preview generation
- Cleanup and maintenance tasks
"""

import logging
import hashlib
from typing import Dict, Any, List, Optional
from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.db import transaction

from .models import PDFTemplate, GeneratedCode, TemplatePreview, TemplateVersion
from .services import PDFGenerationService, TemplateCompilationService
from .engines import EngineError, ValidationError

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_code_async(self, template_id: str, language: str, 
                       options: Dict[str, Any] = None, user_id: int = None):
    """
    Asynchronously generate code for a PDF template.
    
    Args:
        template_id: UUID of the PDFTemplate
        language: Target programming language
        options: Generation options
        user_id: ID of the user requesting generation
        
    Returns:
        Dict with generation results
    """
    try:
        # Get template
        template = PDFTemplate.objects.get(id=template_id)
        
        # Create options hash for caching
        options = options or {}
        options_str = str(sorted(options.items()))
        options_hash = hashlib.md5(options_str.encode()).hexdigest()
        
        # Check cache first
        service = PDFGenerationService()
        cached_code = service.get_cached_generation(template, language, options_hash)
        
        if cached_code:
            logger.info(f"Using cached code generation for template {template_id}")
            return {
                'success': True,
                'generated_code_id': str(cached_code.id),
                'cached': True,
                'generation_time': 0
            }
        
        # Generate new code
        logger.info(f"Starting code generation for template {template_id} in {language}")
        
        generated_code = service.generate_code(template, language, options)
        
        # Cache the result
        service.cache_generation(generated_code, options_hash)
        
        # Send notification email if user provided
        if user_id and generated_code.status == GeneratedCode.Status.COMPLETED:
            send_generation_complete_email.delay(user_id, str(generated_code.id))
        
        return {
            'success': True,
            'generated_code_id': str(generated_code.id),
            'cached': False,
            'generation_time': generated_code.generation_time
        }
        
    except PDFTemplate.DoesNotExist:
        logger.error(f"Template {template_id} not found")
        return {'success': False, 'error': 'Template not found'}
        
    except (ValidationError, EngineError) as e:
        logger.error(f"Code generation failed for template {template_id}: {e}")
        return {'success': False, 'error': str(e)}
        
    except Exception as e:
        logger.error(f"Unexpected error in code generation: {e}")
        # Retry the task
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return {'success': False, 'error': f'Generation failed after retries: {e}'}


@shared_task(bind=True, max_retries=2)
def generate_preview_async(self, template_id: str, sample_data: Dict[str, Any] = None, 
                          user_id: int = None):
    """
    Asynchronously generate template preview.
    
    Args:
        template_id: UUID of the PDFTemplate
        sample_data: Sample data for preview
        user_id: ID of the user requesting preview
        
    Returns:
        Dict with preview results
    """
    try:
        # Get template
        template = PDFTemplate.objects.get(id=template_id)
        
        logger.info(f"Starting preview generation for template {template_id}")
        
        # Generate preview
        service = PDFGenerationService()
        preview = service.create_template_preview(template, sample_data)
        
        # Send notification email if user provided
        if user_id and preview.status == TemplatePreview.PreviewStatus.COMPLETED:
            send_preview_complete_email.delay(user_id, str(preview.id))
        
        return {
            'success': True,
            'preview_id': str(preview.id),
            'generation_time': preview.generation_time
        }
        
    except PDFTemplate.DoesNotExist:
        logger.error(f"Template {template_id} not found")
        return {'success': False, 'error': 'Template not found'}
        
    except Exception as e:
        logger.error(f"Preview generation failed for template {template_id}: {e}")
        # Retry the task
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=30 * (2 ** self.request.retries))
        return {'success': False, 'error': f'Preview generation failed: {e}'}


@shared_task
def batch_generate_code(template_ids: List[str], language: str, 
                       options: Dict[str, Any] = None):
    """
    Generate code for multiple templates in batch.
    
    Args:
        template_ids: List of template UUIDs
        language: Target programming language
        options: Generation options
        
    Returns:
        Dict with batch results
    """
    results = {
        'total': len(template_ids),
        'successful': 0,
        'failed': 0,
        'errors': []
    }
    
    for template_id in template_ids:
        try:
            result = generate_code_async.delay(template_id, language, options)
            if result.get('success'):
                results['successful'] += 1
            else:
                results['failed'] += 1
                results['errors'].append(f"Template {template_id}: {result.get('error')}")
                
        except Exception as e:
            results['failed'] += 1
            results['errors'].append(f"Template {template_id}: {e}")
    
    logger.info(f"Batch generation completed: {results['successful']}/{results['total']} successful")
    
    return results


@shared_task
def compile_template_async(template_id: str):
    """
    Asynchronously compile and optimize a template.
    
    Args:
        template_id: UUID of the PDFTemplate
        
    Returns:
        Dict with compilation results
    """
    try:
        template = PDFTemplate.objects.get(id=template_id)
        
        logger.info(f"Starting template compilation for {template_id}")
        
        # Compile template
        service = TemplateCompilationService()
        compiled_data = service.compile_template(template)
        
        # Update template metadata with compilation info
        template.metadata = template.metadata or {}
        template.metadata['compiled_at'] = timezone.now().isoformat()
        template.metadata['compiled_size'] = len(str(compiled_data))
        template.save(update_fields=['metadata'])
        
        return {
            'success': True,
            'compiled_size': len(str(compiled_data)),
            'element_count': len(compiled_data.get('elements', []))
        }
        
    except PDFTemplate.DoesNotExist:
        logger.error(f"Template {template_id} not found")
        return {'success': False, 'error': 'Template not found'}
        
    except ValidationError as e:
        logger.error(f"Template compilation failed: {e}")
        return {'success': False, 'error': str(e)}
        
    except Exception as e:
        logger.error(f"Unexpected error in template compilation: {e}")
        return {'success': False, 'error': str(e)}


@shared_task
def create_template_version_async(template_id: str, change_type: str = 'updated',
                                 change_description: str = ''):
    """
    Asynchronously create template version.
    
    Args:
        template_id: UUID of the PDFTemplate
        change_type: Type of change
        change_description: Description of changes
        
    Returns:
        Dict with version creation results
    """
    try:
        template = PDFTemplate.objects.get(id=template_id)
        
        logger.info(f"Creating version for template {template_id}")
        
        # Create version
        service = PDFGenerationService()
        version = service.create_template_version(template, change_type, change_description)
        
        return {
            'success': True,
            'version_id': str(version.id),
            'version_number': version.version_number
        }
        
    except PDFTemplate.DoesNotExist:
        logger.error(f"Template {template_id} not found")
        return {'success': False, 'error': 'Template not found'}
        
    except Exception as e:
        logger.error(f"Version creation failed: {e}")
        return {'success': False, 'error': str(e)}


@shared_task
def send_generation_complete_email(user_id: int, generated_code_id: str):
    """
    Send email notification when code generation is complete.
    
    Args:
        user_id: ID of the user to notify
        generated_code_id: UUID of the GeneratedCode
    """
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user = User.objects.get(id=user_id)
        generated_code = GeneratedCode.objects.get(id=generated_code_id)
        
        subject = f'PDF Code Generation Complete - {generated_code.template.name}'
        message = f"""
Hello {user.get_display_name()},

Your PDF code generation is complete!

Template: {generated_code.template.name}
Language: {generated_code.get_language_display()}
Status: {generated_code.get_status_display()}
Generation Time: {generated_code.generation_time:.2f} seconds

You can download your generated code package from the PDFlex dashboard.

Best regards,
PDFlex Team
        """.strip()
        
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False
        )
        
        logger.info(f"Generation complete email sent to {user.email}")
        
    except Exception as e:
        logger.error(f"Failed to send generation complete email: {e}")


@shared_task
def send_preview_complete_email(user_id: int, preview_id: str):
    """
    Send email notification when preview generation is complete.
    
    Args:
        user_id: ID of the user to notify
        preview_id: UUID of the TemplatePreview
    """
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        user = User.objects.get(id=user_id)
        preview = TemplatePreview.objects.get(id=preview_id)
        
        subject = f'PDF Preview Ready - {preview.template.name}'
        message = f"""
Hello {user.get_display_name()},

Your PDF template preview is ready!

Template: {preview.template.name}
Status: {preview.get_status_display()}
Generation Time: {preview.generation_time:.2f} seconds

You can view your preview from the PDFlex dashboard.

Best regards,
PDFlex Team
        """.strip()
        
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False
        )
        
        logger.info(f"Preview complete email sent to {user.email}")
        
    except Exception as e:
        logger.error(f"Failed to send preview complete email: {e}")


@shared_task
def cleanup_expired_generated_code():
    """
    Clean up expired generated code packages.
    
    This task should be run periodically to remove old generated code
    packages and free up storage space.
    """
    try:
        # Delete expired generated code
        expired_count = 0
        
        expired_codes = GeneratedCode.objects.filter(
            expires_at__lt=timezone.now()
        ).exclude(expires_at__isnull=True)
        
        for code in expired_codes:
            if code.code_package:
                code.code_package.delete()
            code.delete()
            expired_count += 1
        
        # Delete failed generations older than 7 days
        failed_cutoff = timezone.now() - timezone.timedelta(days=7)
        failed_codes = GeneratedCode.objects.filter(
            status=GeneratedCode.Status.FAILED,
            created_at__lt=failed_cutoff
        )
        
        failed_count = failed_codes.count()
        failed_codes.delete()
        
        logger.info(f"Cleanup completed: {expired_count} expired, {failed_count} failed codes removed")
        
        return {
            'success': True,
            'expired_removed': expired_count,
            'failed_removed': failed_count
        }
        
    except Exception as e:
        logger.error(f"Cleanup task failed: {e}")
        return {'success': False, 'error': str(e)}


@shared_task
def cleanup_old_previews():
    """
    Clean up old template previews.
    
    Removes preview files older than 30 days to free up storage.
    """
    try:
        cutoff_date = timezone.now() - timezone.timedelta(days=30)
        
        old_previews = TemplatePreview.objects.filter(
            created_at__lt=cutoff_date
        )
        
        removed_count = 0
        for preview in old_previews:
            if preview.preview_pdf:
                preview.preview_pdf.delete()
            if preview.preview_image:
                preview.preview_image.delete()
            preview.delete()
            removed_count += 1
        
        logger.info(f"Preview cleanup completed: {removed_count} old previews removed")
        
        return {
            'success': True,
            'removed_count': removed_count
        }
        
    except Exception as e:
        logger.error(f"Preview cleanup task failed: {e}")
        return {'success': False, 'error': str(e)}
