"""
PDF Generation Services

This module provides high-level services for PDF template processing,
code generation, and package creation with comprehensive error handling
and logging.
"""

import os
import tempfile
import zipfile
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction

from .models import PDFTemplate, GeneratedCode, TemplatePreview, TemplateVersion
from .engines import get_engine, get_supported_languages, EngineError, ValidationError
from .engines.template_renderer import TemplateRenderer

logger = logging.getLogger(__name__)


class PDFGenerationService:
    """
    Main service class for PDF template processing and code generation.
    
    Provides high-level methods for:
    - Template compilation and validation
    - Code generation for target languages
    - ZIP package creation with generated code
    - GitHub repository export functionality
    """
    
    def __init__(self):
        self.template_renderer = TemplateRenderer()
        self.cache_timeout = getattr(settings, 'PDF_GENERATION_CACHE_TIMEOUT', 3600)
    
    def validate_template(self, template: PDFTemplate) -> Tuple[bool, List[str]]:
        """
        Validate a PDF template configuration.
        
        Args:
            template: PDFTemplate instance to validate
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        try:
            template_data = self._serialize_template(template)
            
            # Basic template validation
            errors = []
            
            if not template_data.get('name'):
                errors.append("Template name is required")
            
            if not template_data.get('elements'):
                errors.append("Template must have at least one element")
            
            # Validate elements
            elements = template_data.get('elements', [])
            for i, element in enumerate(elements):
                element_errors = self._validate_element(element, i)
                errors.extend(element_errors)
            
            # Validate template inheritance
            if template.parent_template:
                parent_errors = self._validate_template_inheritance(template)
                errors.extend(parent_errors)
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Template validation failed: {e}")
            return False, [f"Validation error: {e}"]
    
    def generate_code(self, template: PDFTemplate, language: str, 
                     options: Dict[str, Any] = None) -> GeneratedCode:
        """
        Generate code for a template in the specified language.
        
        Args:
            template: PDFTemplate instance
            language: Target programming language
            options: Generation options
            
        Returns:
            GeneratedCode instance
            
        Raises:
            ValidationError: If template validation fails
            EngineError: If code generation fails
        """
        options = options or {}
        
        # Validate template first
        is_valid, errors = self.validate_template(template)
        if not is_valid:
            raise ValidationError(f"Template validation failed: {'; '.join(errors)}")
        
        # Check if language is supported
        if language not in get_supported_languages():
            raise ValueError(f"Unsupported language: {language}")
        
        # Create GeneratedCode record
        generated_code = GeneratedCode.objects.create(
            template=template,
            language=language,
            framework_options=options,
            generated_by=template.created_by,
            status=GeneratedCode.Status.GENERATING
        )
        
        try:
            # Get engine and generate code
            engine = get_engine(language)
            template_data = self._serialize_template(template)
            
            start_time = timezone.now()
            
            # Generate code package
            package_content = engine.create_package(template_data, options)
            
            generation_time = (timezone.now() - start_time).total_seconds()
            
            # Save package file
            filename = f"{template.slug}_{language}_{generated_code.id}.zip"
            generated_code.code_package.save(
                filename,
                ContentFile(package_content),
                save=False
            )
            
            # Update status
            generated_code.status = GeneratedCode.Status.COMPLETED
            generated_code.generation_time = generation_time
            generated_code.save()
            
            # Update template usage
            template.increment_usage()
            
            logger.info(f"Code generated successfully for template {template.id} in {language}")
            
            return generated_code
            
        except Exception as e:
            # Update error status
            generated_code.status = GeneratedCode.Status.FAILED
            generated_code.error_message = str(e)
            generated_code.save()
            
            logger.error(f"Code generation failed for template {template.id}: {e}")
            raise EngineError(f"Code generation failed: {e}")
    
    def create_template_preview(self, template: PDFTemplate, 
                               sample_data: Dict[str, Any] = None) -> TemplatePreview:
        """
        Create a preview for a PDF template.
        
        Args:
            template: PDFTemplate instance
            sample_data: Sample data for preview generation
            
        Returns:
            TemplatePreview instance
        """
        sample_data = sample_data or self._get_default_sample_data(template)
        
        # Create preview record
        preview = TemplatePreview.objects.create(
            template=template,
            sample_data=sample_data,
            generated_by=template.created_by,
            status=TemplatePreview.PreviewStatus.GENERATING
        )
        
        try:
            start_time = timezone.now()
            
            # Generate preview using Python engine (fastest for previews)
            engine = get_engine('python')
            template_data = self._serialize_template(template)
            
            # Render template with sample data
            rendered_elements = []
            for element in template_data.get('elements', []):
                rendered_element = self.template_renderer.render_element_content(element, sample_data)
                rendered_elements.append(rendered_element)
            
            template_data['elements'] = rendered_elements
            
            # Generate PDF for preview
            code_files = engine.generate_code(template_data)
            
            # Execute generated code to create PDF
            # This is a simplified version - in production, you'd want to run this in a sandbox
            pdf_content = self._execute_preview_code(code_files, sample_data)
            
            generation_time = (timezone.now() - start_time).total_seconds()
            
            # Save preview files
            if pdf_content:
                filename = f"preview_{template.slug}_{preview.id}.pdf"
                preview.preview_pdf.save(
                    filename,
                    ContentFile(pdf_content),
                    save=False
                )
            
            # Update status
            preview.status = TemplatePreview.PreviewStatus.COMPLETED
            preview.generation_time = generation_time
            preview.save()
            
            logger.info(f"Preview generated successfully for template {template.id}")
            
            return preview
            
        except Exception as e:
            # Update error status
            preview.status = TemplatePreview.PreviewStatus.FAILED
            preview.error_message = str(e)
            preview.save()
            
            logger.error(f"Preview generation failed for template {template.id}: {e}")
            raise EngineError(f"Preview generation failed: {e}")
    
    def create_template_version(self, template: PDFTemplate, 
                               change_type: str = 'updated',
                               change_description: str = '') -> TemplateVersion:
        """
        Create a version snapshot of a template.
        
        Args:
            template: PDFTemplate instance
            change_type: Type of change (created, updated, published, etc.)
            change_description: Description of changes
            
        Returns:
            TemplateVersion instance
        """
        # Serialize template and elements
        template_data = self._serialize_template(template)
        elements_data = self._serialize_template_elements(template)
        
        # Create version record
        version = TemplateVersion.objects.create(
            template=template,
            version_number=template.version,
            change_type=change_type,
            change_description=change_description,
            template_data=template_data,
            elements_data=elements_data,
            created_by=template.created_by,
            is_current=True
        )
        
        logger.info(f"Version {template.version} created for template {template.id}")
        
        return version
    
    def get_cached_generation(self, template: PDFTemplate, language: str, 
                             options_hash: str) -> Optional[GeneratedCode]:
        """
        Get cached generated code if available.
        
        Args:
            template: PDFTemplate instance
            language: Target programming language
            options_hash: Hash of generation options
            
        Returns:
            GeneratedCode instance if cached, None otherwise
        """
        cache_key = f"generated_code_{template.id}_{language}_{options_hash}"
        cached_id = cache.get(cache_key)
        
        if cached_id:
            try:
                return GeneratedCode.objects.get(
                    id=cached_id,
                    status=GeneratedCode.Status.COMPLETED
                )
            except GeneratedCode.DoesNotExist:
                cache.delete(cache_key)
        
        return None
    
    def cache_generation(self, generated_code: GeneratedCode, options_hash: str):
        """
        Cache generated code for future use.
        
        Args:
            generated_code: GeneratedCode instance
            options_hash: Hash of generation options
        """
        if generated_code.status == GeneratedCode.Status.COMPLETED:
            cache_key = f"generated_code_{generated_code.template.id}_{generated_code.language}_{options_hash}"
            cache.set(cache_key, generated_code.id, self.cache_timeout)
    
    def _serialize_template(self, template: PDFTemplate) -> Dict[str, Any]:
        """Serialize template to dictionary."""
        return {
            'id': str(template.id),
            'name': template.name,
            'description': template.description,
            'page_size': template.page_size,
            'orientation': template.orientation,
            'custom_width': template.custom_width,
            'custom_height': template.custom_height,
            'margin_top': template.margin_top,
            'margin_bottom': template.margin_bottom,
            'margin_left': template.margin_left,
            'margin_right': template.margin_right,
            'settings': template.settings,
            'metadata': template.metadata,
            'elements': self._serialize_template_elements(template),
        }
    
    def _serialize_template_elements(self, template: PDFTemplate) -> List[Dict[str, Any]]:
        """Serialize template elements to list of dictionaries."""
        elements = []
        
        for element in template.elements.all().order_by('order'):
            element_data = {
                'id': str(element.id),
                'name': element.name,
                'element_type': element.element_type,
                'x': element.x,
                'y': element.y,
                'width': element.width,
                'height': element.height,
                'style': element.style,
                'condition': element.condition,
                'data_source': element.data_source,
                'visible': element.visible,
                'order': element.order,
            }
            
            # Add type-specific properties
            if hasattr(element, 'text_properties'):
                element_data['text_properties'] = self._serialize_text_properties(element.text_properties)
            elif hasattr(element, 'image_properties'):
                element_data['image_properties'] = self._serialize_image_properties(element.image_properties)
            elif hasattr(element, 'table_properties'):
                element_data['table_properties'] = self._serialize_table_properties(element.table_properties)
            elif hasattr(element, 'container_properties'):
                element_data['container_properties'] = self._serialize_container_properties(element.container_properties)
            
            elements.append(element_data)
        
        return elements

    def _serialize_text_properties(self, text_props) -> Dict[str, Any]:
        """Serialize text element properties."""
        return {
            'content': text_props.content,
            'font_family': text_props.font_family,
            'font_size': text_props.font_size,
            'font_weight': text_props.font_weight,
            'font_style': text_props.font_style,
            'text_color': text_props.text_color,
            'text_align': text_props.text_align,
            'line_height': text_props.line_height,
            'underline': text_props.underline,
            'strikethrough': text_props.strikethrough,
            'auto_resize': text_props.auto_resize,
            'max_lines': text_props.max_lines,
            'word_wrap': text_props.word_wrap,
        }

    def _serialize_image_properties(self, image_props) -> Dict[str, Any]:
        """Serialize image element properties."""
        return {
            'image_url': image_props.image_url,
            'image_file': image_props.image_file.url if image_props.image_file else None,
            'image_data_source': image_props.image_data_source,
            'fit_mode': image_props.fit_mode,
            'alignment': image_props.alignment,
            'alt_text': image_props.alt_text,
            'opacity': image_props.opacity,
            'rotation': image_props.rotation,
            'quality': image_props.quality,
        }

    def _serialize_table_properties(self, table_props) -> Dict[str, Any]:
        """Serialize table element properties."""
        return {
            'columns': table_props.columns,
            'data_source': table_props.data_source,
            'header_background_color': table_props.header_background_color,
            'header_text_color': table_props.header_text_color,
            'row_background_color': table_props.row_background_color,
            'alternate_row_color': table_props.alternate_row_color,
            'border_style': table_props.border_style,
            'border_width': table_props.border_width,
            'border_color': table_props.border_color,
            'cell_padding': table_props.cell_padding,
            'cell_spacing': table_props.cell_spacing,
            'show_header': table_props.show_header,
            'stripe_rows': table_props.stripe_rows,
            'repeat_header': table_props.repeat_header,
        }

    def _serialize_container_properties(self, container_props) -> Dict[str, Any]:
        """Serialize container element properties."""
        return {
            'flex_direction': container_props.flex_direction,
            'justify_content': container_props.justify_content,
            'align_items': container_props.align_items,
            'gap': container_props.gap,
            'padding_top': container_props.padding_top,
            'padding_bottom': container_props.padding_bottom,
            'padding_left': container_props.padding_left,
            'padding_right': container_props.padding_right,
            'background_color': container_props.background_color,
            'background_image': container_props.background_image.url if container_props.background_image else None,
            'border_radius': container_props.border_radius,
        }

    def _validate_element(self, element: Dict[str, Any], index: int) -> List[str]:
        """Validate a single template element."""
        errors = []
        prefix = f"Element {index + 1}"

        if not element.get('element_type'):
            errors.append(f"{prefix}: Element type is required")

        if not element.get('name'):
            errors.append(f"{prefix}: Element name is required")

        # Type-specific validation
        element_type = element.get('element_type')
        if element_type == 'text':
            if not element.get('text_properties', {}).get('content'):
                errors.append(f"{prefix}: Text content is required")
        elif element_type == 'image':
            image_props = element.get('image_properties', {})
            if not any([
                image_props.get('image_url'),
                image_props.get('image_file'),
                image_props.get('image_data_source')
            ]):
                errors.append(f"{prefix}: Image source is required")
        elif element_type == 'table':
            table_props = element.get('table_properties', {})
            if not table_props.get('columns'):
                errors.append(f"{prefix}: Table columns are required")
            if not table_props.get('data_source'):
                errors.append(f"{prefix}: Table data source is required")

        return errors

    def _validate_template_inheritance(self, template: PDFTemplate) -> List[str]:
        """Validate template inheritance chain."""
        errors = []
        visited = set()
        current = template

        while current.parent_template:
            if current.parent_template.id in visited:
                errors.append("Circular template inheritance detected")
                break
            visited.add(current.id)
            current = current.parent_template

        return errors

    def _get_default_sample_data(self, template: PDFTemplate) -> Dict[str, Any]:
        """Generate default sample data for template preview."""
        return {
            'title': f'Sample {template.name}',
            'date': timezone.now().strftime('%Y-%m-%d'),
            'content': 'This is sample content for preview generation.',
            'items': [
                {'name': 'Sample Item 1', 'value': 100, 'description': 'First sample item'},
                {'name': 'Sample Item 2', 'value': 200, 'description': 'Second sample item'},
                {'name': 'Sample Item 3', 'value': 150, 'description': 'Third sample item'},
            ],
            'total': 450,
            'customer': {
                'name': 'John Doe',
                'email': '<EMAIL>',
                'address': '123 Main St, Sample City, SC 12345',
                'phone': '+****************'
            },
            'company': {
                'name': 'Sample Company',
                'address': '456 Business Ave, Corporate City, CC 67890',
                'phone': '+****************',
                'email': '<EMAIL>'
            }
        }

    def _execute_preview_code(self, code_files: Dict[str, str], sample_data: Dict[str, Any]) -> bytes:
        """
        Execute generated code to create PDF preview.

        This is a simplified implementation. In production, you'd want to:
        1. Run code in a secure sandbox
        2. Use proper isolation
        3. Handle timeouts and resource limits
        """
        try:
            # For now, return a placeholder
            # In a real implementation, you'd execute the generated Python code
            # in a secure environment and return the generated PDF
            return b'%PDF-1.4\n%Placeholder PDF content for preview'
        except Exception as e:
            logger.error(f"Preview code execution failed: {e}")
            raise EngineError(f"Preview generation failed: {e}")


class TemplateCompilationService:
    """
    Service for compiling and optimizing PDF templates.
    """

    def __init__(self):
        self.renderer = TemplateRenderer()

    def compile_template(self, template: PDFTemplate) -> Dict[str, Any]:
        """
        Compile template with optimizations and validation.

        Args:
            template: PDFTemplate instance

        Returns:
            Compiled template data
        """
        # Get base template data
        service = PDFGenerationService()
        template_data = service._serialize_template(template)

        # Apply template inheritance
        if template.parent_template:
            template_data = self._apply_inheritance(template_data, template.parent_template)

        # Optimize template structure
        template_data = self._optimize_template(template_data)

        # Validate compiled template
        errors = self._validate_compiled_template(template_data)
        if errors:
            raise ValidationError(f"Template compilation failed: {'; '.join(errors)}")

        return template_data

    def _apply_inheritance(self, child_data: Dict[str, Any], parent: PDFTemplate) -> Dict[str, Any]:
        """Apply template inheritance."""
        service = PDFGenerationService()
        parent_data = service._serialize_template(parent)

        # Merge settings (child overrides parent)
        merged_settings = parent_data.get('settings', {}).copy()
        merged_settings.update(child_data.get('settings', {}))
        child_data['settings'] = merged_settings

        # Merge metadata
        merged_metadata = parent_data.get('metadata', {}).copy()
        merged_metadata.update(child_data.get('metadata', {}))
        child_data['metadata'] = merged_metadata

        # Inherit page configuration if not set in child
        for field in ['page_size', 'orientation', 'margin_top', 'margin_bottom', 'margin_left', 'margin_right']:
            if not child_data.get(field) and parent_data.get(field):
                child_data[field] = parent_data[field]

        return child_data

    def _optimize_template(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply template optimizations."""
        # Remove unused elements
        elements = template_data.get('elements', [])
        optimized_elements = [elem for elem in elements if elem.get('visible', True)]

        # Sort elements by rendering order
        optimized_elements.sort(key=lambda x: (x.get('y', 0), x.get('x', 0)))

        # Optimize element properties
        for element in optimized_elements:
            element = self._optimize_element(element)

        template_data['elements'] = optimized_elements

        return template_data

    def _optimize_element(self, element: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize individual element properties."""
        # Remove default values to reduce size
        defaults = {
            'x': 0,
            'y': 0,
            'visible': True,
            'order': 0,
        }

        for key, default_value in defaults.items():
            if element.get(key) == default_value:
                element.pop(key, None)

        # Optimize style properties
        style = element.get('style', {})
        if not style:
            element.pop('style', None)

        return element

    def _validate_compiled_template(self, template_data: Dict[str, Any]) -> List[str]:
        """Validate compiled template."""
        errors = []

        # Check required fields
        required_fields = ['name', 'elements']
        for field in required_fields:
            if not template_data.get(field):
                errors.append(f"Required field '{field}' is missing")

        # Validate elements
        elements = template_data.get('elements', [])
        if not elements:
            errors.append("Template must have at least one element")

        # Check for overlapping elements (warning, not error)
        overlaps = self._check_element_overlaps(elements)
        if overlaps:
            logger.warning(f"Template has overlapping elements: {overlaps}")

        return errors

    def _check_element_overlaps(self, elements: List[Dict[str, Any]]) -> List[str]:
        """Check for overlapping elements."""
        overlaps = []

        for i, elem1 in enumerate(elements):
            for j, elem2 in enumerate(elements[i+1:], i+1):
                if self._elements_overlap(elem1, elem2):
                    overlaps.append(f"Elements '{elem1.get('name')}' and '{elem2.get('name')}' overlap")

        return overlaps

    def _elements_overlap(self, elem1: Dict[str, Any], elem2: Dict[str, Any]) -> bool:
        """Check if two elements overlap."""
        x1, y1 = elem1.get('x', 0), elem1.get('y', 0)
        w1, h1 = elem1.get('width', 0), elem1.get('height', 0)

        x2, y2 = elem2.get('x', 0), elem2.get('y', 0)
        w2, h2 = elem2.get('width', 0), elem2.get('height', 0)

        # Check if rectangles overlap
        return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)
