from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class PdfGenerationConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "apps.pdf_generation"
    verbose_name = _('PDF Generation Engine')

    def ready(self):
        """Import signal handlers when the app is ready."""
        try:
            import apps.pdf_generation.signals  # noqa F401
        except ImportError:
            pass
