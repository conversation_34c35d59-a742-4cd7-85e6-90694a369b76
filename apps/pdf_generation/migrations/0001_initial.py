# Generated by Django 4.2.23 on 2025-07-10 03:20

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PDFElement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Element name", max_length=100)),
                (
                    "element_type",
                    models.CharField(
                        choices=[
                            ("text", "Text Element"),
                            ("image", "Image Element"),
                            ("table", "Table Element"),
                            ("container", "Layout Container"),
                            ("line", "Line Element"),
                            ("shape", "Shape Element"),
                        ],
                        max_length=20,
                    ),
                ),
                ("order", models.PositiveIntegerField(default=0)),
                (
                    "position_type",
                    models.CharField(
                        choices=[
                            ("absolute", "Absolute Position"),
                            ("relative", "Relative Position"),
                            ("flow", "Flow Layout"),
                        ],
                        default="absolute",
                        max_length=20,
                    ),
                ),
                ("x", models.FloatField(default=0, help_text="X position in mm")),
                ("y", models.FloatField(default=0, help_text="Y position in mm")),
                (
                    "width",
                    models.FloatField(blank=True, help_text="Width in mm", null=True),
                ),
                (
                    "height",
                    models.FloatField(blank=True, help_text="Height in mm", null=True),
                ),
                (
                    "style",
                    models.JSONField(
                        blank=True, default=dict, help_text="Element styling properties"
                    ),
                ),
                (
                    "condition",
                    models.TextField(
                        blank=True,
                        help_text="Condition for rendering this element (Jinja2 expression)",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        blank=True,
                        help_text="Data source path for dynamic content",
                        max_length=200,
                    ),
                ),
                ("visible", models.BooleanField(default=True)),
                ("locked", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "parent_element",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="child_elements",
                        to="pdf_generation.pdfelement",
                    ),
                ),
            ],
            options={
                "verbose_name": "PDF Element",
                "verbose_name_plural": "PDF Elements",
                "ordering": ["order", "created_at"],
            },
        ),
        migrations.CreateModel(
            name="PDFTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(help_text="Template name", max_length=200)),
                ("slug", models.SlugField(blank=True, max_length=220, unique=True)),
                (
                    "description",
                    models.TextField(blank=True, help_text="Template description"),
                ),
                (
                    "organization",
                    models.CharField(
                        blank=True,
                        help_text="Organization or company name",
                        max_length=100,
                    ),
                ),
                (
                    "page_size",
                    models.CharField(
                        choices=[
                            ("A4", "A4 (210 × 297 mm)"),
                            ("A3", "A3 (297 × 420 mm)"),
                            ("A5", "A5 (148 × 210 mm)"),
                            ("LETTER", "Letter (8.5 × 11 in)"),
                            ("LEGAL", "Legal (8.5 × 14 in)"),
                            ("TABLOID", "Tabloid (11 × 17 in)"),
                            ("CUSTOM", "Custom Size"),
                        ],
                        default="A4",
                        max_length=20,
                    ),
                ),
                (
                    "orientation",
                    models.CharField(
                        choices=[("portrait", "Portrait"), ("landscape", "Landscape")],
                        default="portrait",
                        max_length=20,
                    ),
                ),
                (
                    "custom_width",
                    models.FloatField(
                        blank=True,
                        help_text="Custom width in mm",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(50),
                            django.core.validators.MaxValueValidator(2000),
                        ],
                    ),
                ),
                (
                    "custom_height",
                    models.FloatField(
                        blank=True,
                        help_text="Custom height in mm",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(50),
                            django.core.validators.MaxValueValidator(2000),
                        ],
                    ),
                ),
                (
                    "margin_top",
                    models.FloatField(
                        default=20.0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "margin_bottom",
                    models.FloatField(
                        default=20.0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "margin_left",
                    models.FloatField(
                        default=20.0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "margin_right",
                    models.FloatField(
                        default=20.0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("version", models.CharField(default="1.0.0", max_length=20)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("active", "Active"),
                            ("archived", "Archived"),
                            ("deprecated", "Deprecated"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional template metadata",
                    ),
                ),
                (
                    "settings",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Template-specific settings and configurations",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("usage_count", models.PositiveIntegerField(default=0)),
                ("last_used", models.DateTimeField(blank=True, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="pdf_templates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent_template",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent template for inheritance",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="child_templates",
                        to="pdf_generation.pdftemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "PDF Template",
                "verbose_name_plural": "PDF Templates",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="TextElement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        help_text="Text content (supports Jinja2 templates)"
                    ),
                ),
                ("font_family", models.CharField(default="Arial", max_length=100)),
                (
                    "font_size",
                    models.FloatField(
                        default=12,
                        validators=[
                            django.core.validators.MinValueValidator(6),
                            django.core.validators.MaxValueValidator(72),
                        ],
                    ),
                ),
                (
                    "font_weight",
                    models.CharField(
                        choices=[
                            ("normal", "Normal"),
                            ("bold", "Bold"),
                            ("light", "Light"),
                        ],
                        default="normal",
                        max_length=20,
                    ),
                ),
                (
                    "font_style",
                    models.CharField(
                        choices=[
                            ("normal", "Normal"),
                            ("italic", "Italic"),
                            ("oblique", "Oblique"),
                        ],
                        default="normal",
                        max_length=20,
                    ),
                ),
                ("text_color", models.CharField(default="#000000", max_length=7)),
                (
                    "text_align",
                    models.CharField(
                        choices=[
                            ("left", "Left"),
                            ("center", "Center"),
                            ("right", "Right"),
                            ("justify", "Justify"),
                        ],
                        default="left",
                        max_length=20,
                    ),
                ),
                (
                    "line_height",
                    models.FloatField(
                        default=1.2,
                        validators=[
                            django.core.validators.MinValueValidator(0.5),
                            django.core.validators.MaxValueValidator(3.0),
                        ],
                    ),
                ),
                ("underline", models.BooleanField(default=False)),
                ("strikethrough", models.BooleanField(default=False)),
                ("auto_resize", models.BooleanField(default=False)),
                ("max_lines", models.PositiveIntegerField(blank=True, null=True)),
                ("word_wrap", models.BooleanField(default=True)),
                (
                    "element",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="text_properties",
                        to="pdf_generation.pdfelement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Text Element",
                "verbose_name_plural": "Text Elements",
            },
        ),
        migrations.CreateModel(
            name="TableElement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "columns",
                    models.JSONField(
                        default=list,
                        help_text="Column definitions with headers, widths, and data sources",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        help_text="Data source path for table rows", max_length=200
                    ),
                ),
                (
                    "header_background_color",
                    models.CharField(default="#f0f0f0", max_length=7),
                ),
                (
                    "header_text_color",
                    models.CharField(default="#000000", max_length=7),
                ),
                (
                    "row_background_color",
                    models.CharField(default="#ffffff", max_length=7),
                ),
                ("alternate_row_color", models.CharField(blank=True, max_length=7)),
                (
                    "border_style",
                    models.CharField(
                        choices=[
                            ("none", "None"),
                            ("solid", "Solid"),
                            ("dashed", "Dashed"),
                            ("dotted", "Dotted"),
                        ],
                        default="solid",
                        max_length=20,
                    ),
                ),
                ("border_width", models.FloatField(default=1.0)),
                ("border_color", models.CharField(default="#000000", max_length=7)),
                ("cell_padding", models.FloatField(default=5.0)),
                ("cell_spacing", models.FloatField(default=0.0)),
                ("show_header", models.BooleanField(default=True)),
                ("stripe_rows", models.BooleanField(default=False)),
                ("repeat_header", models.BooleanField(default=True)),
                (
                    "element",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="table_properties",
                        to="pdf_generation.pdfelement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Table Element",
                "verbose_name_plural": "Table Elements",
            },
        ),
        migrations.AddField(
            model_name="pdfelement",
            name="template",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="elements",
                to="pdf_generation.pdftemplate",
            ),
        ),
        migrations.CreateModel(
            name="ImageElement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image_url", models.URLField(blank=True, help_text="Image URL")),
                (
                    "image_file",
                    models.ImageField(
                        blank=True,
                        help_text="Uploaded image file",
                        null=True,
                        upload_to="pdf_templates/images/",
                    ),
                ),
                (
                    "image_data_source",
                    models.CharField(
                        blank=True,
                        help_text="Dynamic image source path",
                        max_length=200,
                    ),
                ),
                (
                    "fit_mode",
                    models.CharField(
                        choices=[
                            ("contain", "Contain"),
                            ("cover", "Cover"),
                            ("fill", "Fill"),
                            ("scale-down", "Scale Down"),
                            ("none", "None"),
                        ],
                        default="contain",
                        max_length=20,
                    ),
                ),
                (
                    "alignment",
                    models.CharField(
                        choices=[
                            ("top-left", "Top Left"),
                            ("top-center", "Top Center"),
                            ("top-right", "Top Right"),
                            ("center-left", "Center Left"),
                            ("center", "Center"),
                            ("center-right", "Center Right"),
                            ("bottom-left", "Bottom Left"),
                            ("bottom-center", "Bottom Center"),
                            ("bottom-right", "Bottom Right"),
                        ],
                        default="center",
                        max_length=20,
                    ),
                ),
                (
                    "alt_text",
                    models.CharField(
                        blank=True,
                        help_text="Alternative text for accessibility",
                        max_length=200,
                    ),
                ),
                (
                    "opacity",
                    models.FloatField(
                        default=1.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                    ),
                ),
                (
                    "rotation",
                    models.FloatField(
                        default=0,
                        help_text="Rotation angle in degrees",
                        validators=[
                            django.core.validators.MinValueValidator(-360),
                            django.core.validators.MaxValueValidator(360),
                        ],
                    ),
                ),
                (
                    "quality",
                    models.PositiveIntegerField(
                        default=85,
                        help_text="Image quality (1-100)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "element",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="image_properties",
                        to="pdf_generation.pdfelement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Image Element",
                "verbose_name_plural": "Image Elements",
            },
        ),
        migrations.CreateModel(
            name="GeneratedCode",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("python", "Python"),
                            ("javascript", "JavaScript"),
                            ("php", "PHP"),
                            ("java", "Java"),
                            ("csharp", "C#"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "framework_options",
                    models.JSONField(
                        default=dict,
                        help_text="Framework-specific options and configurations",
                    ),
                ),
                (
                    "code_package",
                    models.FileField(
                        blank=True,
                        help_text="Generated code package (ZIP file)",
                        null=True,
                        upload_to="generated_code/",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                ("generation_time", models.FloatField(blank=True, null=True)),
                ("download_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_code_packages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_code",
                        to="pdf_generation.pdftemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Generated Code",
                "verbose_name_plural": "Generated Code Packages",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContainerElement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "flex_direction",
                    models.CharField(
                        choices=[
                            ("row", "Row"),
                            ("column", "Column"),
                            ("row-reverse", "Row Reverse"),
                            ("column-reverse", "Column Reverse"),
                        ],
                        default="column",
                        max_length=20,
                    ),
                ),
                (
                    "justify_content",
                    models.CharField(
                        choices=[
                            ("flex-start", "Flex Start"),
                            ("flex-end", "Flex End"),
                            ("center", "Center"),
                            ("space-between", "Space Between"),
                            ("space-around", "Space Around"),
                            ("space-evenly", "Space Evenly"),
                        ],
                        default="flex-start",
                        max_length=20,
                    ),
                ),
                (
                    "align_items",
                    models.CharField(
                        choices=[
                            ("flex-start", "Flex Start"),
                            ("flex-end", "Flex End"),
                            ("center", "Center"),
                            ("stretch", "Stretch"),
                            ("baseline", "Baseline"),
                        ],
                        default="stretch",
                        max_length=20,
                    ),
                ),
                (
                    "gap",
                    models.FloatField(
                        default=0, help_text="Gap between child elements in mm"
                    ),
                ),
                ("padding_top", models.FloatField(default=0)),
                ("padding_bottom", models.FloatField(default=0)),
                ("padding_left", models.FloatField(default=0)),
                ("padding_right", models.FloatField(default=0)),
                ("background_color", models.CharField(blank=True, max_length=7)),
                (
                    "background_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="pdf_templates/backgrounds/"
                    ),
                ),
                ("border_radius", models.FloatField(default=0)),
                (
                    "element",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="container_properties",
                        to="pdf_generation.pdfelement",
                    ),
                ),
            ],
            options={
                "verbose_name": "Container Element",
                "verbose_name_plural": "Container Elements",
            },
        ),
        migrations.CreateModel(
            name="TemplateVersion",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("version_number", models.CharField(max_length=20)),
                (
                    "change_type",
                    models.CharField(
                        choices=[
                            ("created", "Created"),
                            ("updated", "Updated"),
                            ("published", "Published"),
                            ("archived", "Archived"),
                            ("restored", "Restored"),
                        ],
                        max_length=20,
                    ),
                ),
                ("change_description", models.TextField(blank=True)),
                (
                    "template_data",
                    models.JSONField(
                        help_text="Complete template configuration snapshot"
                    ),
                ),
                (
                    "elements_data",
                    models.JSONField(help_text="All template elements snapshot"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_current", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="template_versions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "rollback_from",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="rollbacks",
                        to="pdf_generation.templateversion",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versions",
                        to="pdf_generation.pdftemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Template Version",
                "verbose_name_plural": "Template Versions",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["template", "is_current"],
                        name="pdf_generat_templat_581867_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="pdf_generat_created_8e29bf_idx"
                    ),
                ],
                "unique_together": {("template", "version_number")},
            },
        ),
        migrations.CreateModel(
            name="TemplatePreview",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "sample_data",
                    models.JSONField(
                        default=dict, help_text="Sample data for preview generation"
                    ),
                ),
                (
                    "preview_image",
                    models.ImageField(
                        blank=True,
                        help_text="Preview image (PNG/JPEG)",
                        null=True,
                        upload_to="pdf_templates/previews/",
                    ),
                ),
                (
                    "preview_pdf",
                    models.FileField(
                        blank=True,
                        help_text="Preview PDF file",
                        null=True,
                        upload_to="pdf_templates/previews/",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("generating", "Generating"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                (
                    "generation_time",
                    models.FloatField(
                        blank=True, help_text="Generation time in seconds", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "generated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generated_previews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="previews",
                        to="pdf_generation.pdftemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Template Preview",
                "verbose_name_plural": "Template Previews",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["template", "status"],
                        name="pdf_generat_templat_0df20b_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="pdf_generat_created_2cc66b_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="pdftemplate",
            index=models.Index(
                fields=["status", "created_by"], name="pdf_generat_status_e7bc80_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pdftemplate",
            index=models.Index(fields=["slug"], name="pdf_generat_slug_d2ebcd_idx"),
        ),
        migrations.AddIndex(
            model_name="pdftemplate",
            index=models.Index(
                fields=["created_at"], name="pdf_generat_created_34ad63_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pdfelement",
            index=models.Index(
                fields=["template", "element_type"],
                name="pdf_generat_templat_d6ae9a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="pdfelement",
            index=models.Index(
                fields=["parent_element", "order"],
                name="pdf_generat_parent__f5541a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="generatedcode",
            index=models.Index(
                fields=["template", "language"], name="pdf_generat_templat_88b558_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="generatedcode",
            index=models.Index(fields=["status"], name="pdf_generat_status_5df1af_idx"),
        ),
        migrations.AddIndex(
            model_name="generatedcode",
            index=models.Index(
                fields=["expires_at"], name="pdf_generat_expires_0ec238_idx"
            ),
        ),
    ]
