#!/usr/bin/env python
"""
Test runner for PDF generation app.

This script provides utilities for running tests with different configurations
and generating test reports.
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner
from django.core.management import execute_from_command_line


def setup_test_environment():
    """Set up Django test environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')
    django.setup()


def run_tests(test_labels=None, verbosity=1, interactive=True, failfast=False, keepdb=False):
    """
    Run tests for the PDF generation app.
    
    Args:
        test_labels: List of test labels to run (default: all pdf_generation tests)
        verbosity: Verbosity level (0-3)
        interactive: Whether to run in interactive mode
        failfast: Whether to stop on first failure
        keepdb: Whether to preserve test database
    """
    setup_test_environment()
    
    if test_labels is None:
        test_labels = ['apps.pdf_generation.tests']
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(
        verbosity=verbosity,
        interactive=interactive,
        failfast=failfast,
        keepdb=keepdb
    )
    
    failures = test_runner.run_tests(test_labels)
    
    if failures:
        sys.exit(1)


def run_coverage_tests():
    """Run tests with coverage reporting."""
    try:
        import coverage
    except ImportError:
        print("Coverage.py not installed. Install with: pip install coverage")
        sys.exit(1)
    
    # Start coverage
    cov = coverage.Coverage(
        source=['apps/pdf_generation'],
        omit=[
            '*/tests.py',
            '*/test_*.py',
            '*/migrations/*',
            '*/venv/*',
            '*/virtualenv/*'
        ]
    )
    cov.start()
    
    # Run tests
    try:
        run_tests(verbosity=2)
    finally:
        # Stop coverage and generate report
        cov.stop()
        cov.save()
        
        print("\n" + "="*50)
        print("COVERAGE REPORT")
        print("="*50)
        cov.report()
        
        # Generate HTML report
        html_dir = 'htmlcov'
        cov.html_report(directory=html_dir)
        print(f"\nHTML coverage report generated in {html_dir}/")


def run_performance_tests():
    """Run performance tests."""
    setup_test_environment()
    
    print("Running performance tests...")
    
    # Import performance test modules
    from .tests import IntegrationTest
    import time
    import unittest
    
    # Create test suite with performance tests
    suite = unittest.TestSuite()
    
    # Add performance-critical tests
    suite.addTest(IntegrationTest('test_complete_workflow'))
    
    # Run with timing
    runner = unittest.TextTestRunner(verbosity=2)
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    print(f"\nPerformance test completed in {end_time - start_time:.2f} seconds")
    
    if not result.wasSuccessful():
        sys.exit(1)


def run_integration_tests():
    """Run integration tests only."""
    test_labels = [
        'apps.pdf_generation.tests.IntegrationTest',
        'apps.pdf_generation.tests.PDFGenerationServiceTest',
        'apps.pdf_generation.tests.TemplateCompilationServiceTest'
    ]
    
    print("Running integration tests...")
    run_tests(test_labels, verbosity=2)


def run_unit_tests():
    """Run unit tests only."""
    test_labels = [
        'apps.pdf_generation.tests.PDFTemplateModelTest',
        'apps.pdf_generation.tests.PDFElementModelTest',
        'apps.pdf_generation.tests.TemplateRendererTest',
        'apps.pdf_generation.tests.PDFEngineTest'
    ]
    
    print("Running unit tests...")
    run_tests(test_labels, verbosity=2)


def run_load_tests():
    """Run load tests to check system performance under stress."""
    setup_test_environment()
    
    print("Running load tests...")
    
    from django.contrib.auth import get_user_model
    from .models import PDFTemplate, PDFElement, TextElement
    from .services import PDFGenerationService
    import time
    import threading
    from unittest.mock import patch, MagicMock
    
    User = get_user_model()
    
    # Create test user and template
    user = User.objects.create_user(
        username='loadtest',
        email='<EMAIL>',
        password='testpass123'
    )
    
    template = PDFTemplate.objects.create(
        name='Load Test Template',
        created_by=user
    )
    
    element = PDFElement.objects.create(
        template=template,
        name='Test Element',
        element_type=PDFElement.ElementType.TEXT
    )
    
    TextElement.objects.create(
        element=element,
        content='Load test content'
    )
    
    service = PDFGenerationService()
    
    # Test concurrent template validation
    def validate_template():
        return service.validate_template(template)
    
    # Run concurrent validations
    threads = []
    results = []
    start_time = time.time()
    
    for i in range(10):
        thread = threading.Thread(target=lambda: results.append(validate_template()))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    print(f"Validated template {len(results)} times concurrently in {end_time - start_time:.2f} seconds")
    print(f"Average time per validation: {(end_time - start_time) / len(results):.4f} seconds")
    
    # Check all validations succeeded
    all_valid = all(result[0] for result in results)
    print(f"All validations successful: {all_valid}")
    
    if not all_valid:
        print("Some validations failed!")
        sys.exit(1)


def main():
    """Main test runner entry point."""
    if len(sys.argv) < 2:
        print("Usage: python test_runner.py <command>")
        print("Commands:")
        print("  all          - Run all tests")
        print("  unit         - Run unit tests only")
        print("  integration  - Run integration tests only")
        print("  coverage     - Run tests with coverage reporting")
        print("  performance  - Run performance tests")
        print("  load         - Run load tests")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == 'all':
        run_tests()
    elif command == 'unit':
        run_unit_tests()
    elif command == 'integration':
        run_integration_tests()
    elif command == 'coverage':
        run_coverage_tests()
    elif command == 'performance':
        run_performance_tests()
    elif command == 'load':
        run_load_tests()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)


if __name__ == '__main__':
    main()
