"""
Comprehensive tests for PDF generation system.

This module contains unit tests, integration tests, and test fixtures
for all components of the PDF generation engine.
"""

import json
import tempfile
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.exceptions import ValidationError
from django.utils import timezone

from .models import (
    PDFTemplate, PDFElement, TextElement, ImageElement,
    TableElement, ContainerElement, TemplatePreview,
    TemplateVersion, GeneratedCode
)
from .services import PDFGenerationService, TemplateCompilationService
from .engines import get_engine, get_supported_languages, EngineError
from .engines.template_renderer import TemplateRenderer

User = get_user_model()


class PDFTemplateModelTest(TestCase):
    """Test cases for PDFTemplate model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.template_data = {
            'name': 'Test Template',
            'description': 'A test PDF template',
            'created_by': self.user,
            'page_size': PDFTemplate.PageSize.A4,
            'orientation': PDFTemplate.Orientation.PORTRAIT,
        }

    def test_template_creation(self):
        """Test basic template creation."""
        template = PDFTemplate.objects.create(**self.template_data)

        self.assertEqual(template.name, 'Test Template')
        self.assertEqual(template.created_by, self.user)
        self.assertEqual(template.status, PDFTemplate.Status.DRAFT)
        self.assertIsNotNone(template.slug)
        self.assertEqual(template.version, '1.0.0')

    def test_slug_generation(self):
        """Test automatic slug generation."""
        template = PDFTemplate.objects.create(**self.template_data)
        self.assertEqual(template.slug, 'test-template')

        # Test slug uniqueness
        template2_data = self.template_data.copy()
        template2_data['name'] = 'Test Template'  # Same name
        template2 = PDFTemplate.objects.create(**template2_data)
        self.assertEqual(template2.slug, 'test-template-1')

    def test_custom_page_size_validation(self):
        """Test custom page size validation."""
        template_data = self.template_data.copy()
        template_data['page_size'] = PDFTemplate.PageSize.CUSTOM

        # Should raise validation error without custom dimensions
        template = PDFTemplate(**template_data)
        with self.assertRaises(ValidationError):
            template.clean()

        # Should pass with custom dimensions
        template_data['custom_width'] = 200
        template_data['custom_height'] = 300
        template = PDFTemplate(**template_data)
        template.clean()  # Should not raise

    def test_template_inheritance_validation(self):
        """Test template inheritance validation."""
        parent = PDFTemplate.objects.create(**self.template_data)

        child_data = self.template_data.copy()
        child_data['name'] = 'Child Template'
        child_data['parent_template'] = parent
        child = PDFTemplate.objects.create(**child_data)

        # Test circular inheritance prevention
        parent.parent_template = child
        with self.assertRaises(ValidationError):
            parent.clean()

    def test_page_dimensions(self):
        """Test page dimension calculation."""
        template = PDFTemplate.objects.create(**self.template_data)

        # Test A4 portrait
        width, height = template.get_page_dimensions()
        self.assertEqual(width, 210)
        self.assertEqual(height, 297)

        # Test A4 landscape
        template.orientation = PDFTemplate.Orientation.LANDSCAPE
        width, height = template.get_page_dimensions()
        self.assertEqual(width, 297)
        self.assertEqual(height, 210)

        # Test custom size
        template.page_size = PDFTemplate.PageSize.CUSTOM
        template.custom_width = 150
        template.custom_height = 200
        width, height = template.get_page_dimensions()
        self.assertEqual(width, 150)
        self.assertEqual(height, 200)

    def test_usage_increment(self):
        """Test usage count increment."""
        template = PDFTemplate.objects.create(**self.template_data)
        initial_count = template.usage_count
        initial_last_used = template.last_used

        template.increment_usage()
        template.refresh_from_db()

        self.assertEqual(template.usage_count, initial_count + 1)
        self.assertIsNotNone(template.last_used)
        self.assertNotEqual(template.last_used, initial_last_used)


class PDFElementModelTest(TestCase):
    """Test cases for PDFElement and related models."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.template = PDFTemplate.objects.create(
            name='Test Template',
            created_by=self.user
        )

    def test_element_creation(self):
        """Test basic element creation."""
        element = PDFElement.objects.create(
            template=self.template,
            name='Test Element',
            element_type=PDFElement.ElementType.TEXT,
            x=10,
            y=20,
            width=100,
            height=30
        )

        self.assertEqual(element.template, self.template)
        self.assertEqual(element.name, 'Test Element')
        self.assertEqual(element.element_type, PDFElement.ElementType.TEXT)
        self.assertTrue(element.visible)

    def test_text_element_creation(self):
        """Test text element creation."""
        element = PDFElement.objects.create(
            template=self.template,
            name='Text Element',
            element_type=PDFElement.ElementType.TEXT
        )

        text_element = TextElement.objects.create(
            element=element,
            content='Hello, World!',
            font_family='Arial',
            font_size=12,
            text_color='#000000'
        )

        self.assertEqual(text_element.element, element)
        self.assertEqual(text_element.content, 'Hello, World!')
        self.assertEqual(text_element.font_family, 'Arial')

    def test_image_element_creation(self):
        """Test image element creation."""
        element = PDFElement.objects.create(
            template=self.template,
            name='Image Element',
            element_type=PDFElement.ElementType.IMAGE
        )

        # Create a simple test image file
        image_content = b'fake image content'
        image_file = SimpleUploadedFile(
            'test_image.jpg',
            image_content,
            content_type='image/jpeg'
        )

        image_element = ImageElement.objects.create(
            element=element,
            image_file=image_file,
            fit_mode=ImageElement.FitMode.CONTAIN,
            alt_text='Test image'
        )

        self.assertEqual(image_element.element, element)
        self.assertEqual(image_element.fit_mode, ImageElement.FitMode.CONTAIN)
        self.assertEqual(image_element.alt_text, 'Test image')

    def test_table_element_creation(self):
        """Test table element creation."""
        element = PDFElement.objects.create(
            template=self.template,
            name='Table Element',
            element_type=PDFElement.ElementType.TABLE
        )

        columns = [
            {
                'name': 'column1',
                'header': 'Column 1',
                'width': 100,
                'data_source': 'data.column1'
            },
            {
                'name': 'column2',
                'header': 'Column 2',
                'width': 150,
                'data_source': 'data.column2'
            }
        ]

        table_element = TableElement.objects.create(
            element=element,
            columns=columns,
            data_source='data.items',
            show_header=True
        )

        self.assertEqual(table_element.element, element)
        self.assertEqual(len(table_element.columns), 2)
        self.assertEqual(table_element.data_source, 'data.items')
        self.assertTrue(table_element.show_header)

    def test_container_element_creation(self):
        """Test container element creation."""
        element = PDFElement.objects.create(
            template=self.template,
            name='Container Element',
            element_type=PDFElement.ElementType.CONTAINER
        )

        container_element = ContainerElement.objects.create(
            element=element,
            flex_direction=ContainerElement.FlexDirection.COLUMN,
            justify_content=ContainerElement.JustifyContent.CENTER,
            gap=10
        )

        self.assertEqual(container_element.element, element)
        self.assertEqual(container_element.flex_direction, ContainerElement.FlexDirection.COLUMN)
        self.assertEqual(container_element.gap, 10)

    def test_element_hierarchy(self):
        """Test element parent-child relationships."""
        parent_element = PDFElement.objects.create(
            template=self.template,
            name='Parent Element',
            element_type=PDFElement.ElementType.CONTAINER
        )

        child_element = PDFElement.objects.create(
            template=self.template,
            name='Child Element',
            element_type=PDFElement.ElementType.TEXT,
            parent_element=parent_element
        )

        self.assertEqual(child_element.parent_element, parent_element)
        self.assertIn(child_element, parent_element.child_elements.all())

    def test_element_ordering(self):
        """Test element ordering."""
        element1 = PDFElement.objects.create(
            template=self.template,
            name='Element 1',
            element_type=PDFElement.ElementType.TEXT,
            order=2
        )

        element2 = PDFElement.objects.create(
            template=self.template,
            name='Element 2',
            element_type=PDFElement.ElementType.TEXT,
            order=1
        )

        elements = list(self.template.elements.all())
        self.assertEqual(elements[0], element2)  # Lower order comes first
        self.assertEqual(elements[1], element1)


class PDFGenerationServiceTest(TestCase):
    """Test cases for PDFGenerationService."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.template = PDFTemplate.objects.create(
            name='Test Template',
            created_by=self.user
        )

        self.text_element = PDFElement.objects.create(
            template=self.template,
            name='Text Element',
            element_type=PDFElement.ElementType.TEXT,
            x=10,
            y=20,
            width=100,
            height=30
        )

        TextElement.objects.create(
            element=self.text_element,
            content='Hello, {{name}}!',
            font_family='Arial',
            font_size=12
        )

        self.service = PDFGenerationService()

    def test_template_validation_success(self):
        """Test successful template validation."""
        is_valid, errors = self.service.validate_template(self.template)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_template_validation_failure(self):
        """Test template validation with errors."""
        # Create template without elements
        empty_template = PDFTemplate.objects.create(
            name='Empty Template',
            created_by=self.user
        )

        is_valid, errors = self.service.validate_template(empty_template)
        self.assertFalse(is_valid)
        self.assertIn('Template must have at least one element', errors)

    def test_template_serialization(self):
        """Test template serialization."""
        template_data = self.service._serialize_template(self.template)

        self.assertEqual(template_data['name'], 'Test Template')
        self.assertEqual(len(template_data['elements']), 1)
        self.assertEqual(template_data['elements'][0]['name'], 'Text Element')
        self.assertEqual(template_data['elements'][0]['element_type'], 'text')

    @patch('apps.pdf_generation.services.get_engine')
    def test_code_generation_success(self, mock_get_engine):
        """Test successful code generation."""
        # Mock engine
        mock_engine = MagicMock()
        mock_engine.create_package.return_value = b'fake zip content'
        mock_get_engine.return_value = mock_engine

        generated_code = self.service.generate_code(self.template, 'python')

        self.assertEqual(generated_code.template, self.template)
        self.assertEqual(generated_code.language, 'python')
        self.assertEqual(generated_code.status, GeneratedCode.Status.COMPLETED)
        self.assertIsNotNone(generated_code.code_package)

    @patch('apps.pdf_generation.services.get_engine')
    def test_code_generation_failure(self, mock_get_engine):
        """Test code generation failure."""
        # Mock engine to raise error
        mock_engine = MagicMock()
        mock_engine.create_package.side_effect = EngineError('Test error')
        mock_get_engine.return_value = mock_engine

        with self.assertRaises(EngineError):
            self.service.generate_code(self.template, 'python')

    def test_template_version_creation(self):
        """Test template version creation."""
        version = self.service.create_template_version(
            self.template,
            'updated',
            'Test update'
        )

        self.assertEqual(version.template, self.template)
        self.assertEqual(version.change_type, 'updated')
        self.assertEqual(version.change_description, 'Test update')
        self.assertTrue(version.is_current)
        self.assertIsNotNone(version.template_data)
        self.assertIsNotNone(version.elements_data)


class TemplateCompilationServiceTest(TestCase):
    """Test cases for TemplateCompilationService."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.parent_template = PDFTemplate.objects.create(
            name='Parent Template',
            created_by=self.user,
            margin_top=20,
            margin_bottom=20
        )

        self.child_template = PDFTemplate.objects.create(
            name='Child Template',
            created_by=self.user,
            parent_template=self.parent_template,
            margin_left=15  # Override parent setting
        )

        self.service = TemplateCompilationService()

    def test_template_compilation(self):
        """Test basic template compilation."""
        compiled_data = self.service.compile_template(self.child_template)

        self.assertEqual(compiled_data['name'], 'Child Template')
        self.assertIsNotNone(compiled_data['elements'])

    def test_template_inheritance(self):
        """Test template inheritance application."""
        compiled_data = self.service.compile_template(self.child_template)

        # Child should inherit parent's margins but override margin_left
        self.assertEqual(compiled_data['margin_top'], 20)  # From parent
        self.assertEqual(compiled_data['margin_bottom'], 20)  # From parent
        self.assertEqual(compiled_data['margin_left'], 15)  # From child

    def test_template_optimization(self):
        """Test template optimization."""
        # Add elements with default values
        element = PDFElement.objects.create(
            template=self.child_template,
            name='Test Element',
            element_type=PDFElement.ElementType.TEXT,
            x=0,  # Default value
            y=0,  # Default value
            visible=True  # Default value
        )

        compiled_data = self.service.compile_template(self.child_template)

        # Optimized elements should have default values removed
        element_data = compiled_data['elements'][0]
        self.assertNotIn('x', element_data)  # Default value removed
        self.assertNotIn('y', element_data)  # Default value removed
        self.assertNotIn('visible', element_data)  # Default value removed


class TemplateRendererTest(TestCase):
    """Test cases for TemplateRenderer."""

    def setUp(self):
        """Set up test data."""
        self.renderer = TemplateRenderer()

        self.sample_data = {
            'name': 'John Doe',
            'items': [
                {'name': 'Item 1', 'price': 10.50},
                {'name': 'Item 2', 'price': 25.00}
            ],
            'total': 35.50,
            'user': {
                'profile': {
                    'name': 'John Doe'
                }
            }
        }

    def test_basic_template_rendering(self):
        """Test basic template rendering."""
        template = 'Hello, {{name}}!'
        result = self.renderer.render_template(template, self.sample_data)
        self.assertEqual(result, 'Hello, John Doe!')

    def test_loop_rendering(self):
        """Test loop rendering in templates."""
        template = '{% for item in items %}{{item.name}}: ${{item.price}}{% endfor %}'
        result = self.renderer.render_template(template, self.sample_data)
        self.assertIn('Item 1: $10.5', result)
        self.assertIn('Item 2: $25.0', result)

    def test_conditional_rendering(self):
        """Test conditional rendering."""
        template = '{% if total > 30 %}Expensive{% else %}Cheap{% endif %}'
        result = self.renderer.render_template(template, self.sample_data)
        self.assertEqual(result, 'Expensive')

    def test_data_path_resolution(self):
        """Test dot-notation data path resolution."""
        result = self.renderer._resolve_data_path('user.profile.name', self.sample_data)
        self.assertEqual(result, 'John Doe')

        result = self.renderer._resolve_data_path('nonexistent.path', self.sample_data)
        self.assertIsNone(result)

    def test_custom_filters(self):
        """Test custom template filters."""
        # Test currency filter
        template = '{{total|format_currency}}'
        result = self.renderer.render_template(template, self.sample_data)
        self.assertIn('35.50', result)

        # Test number filter
        template = '{{total|format_number:1}}'
        result = self.renderer.render_template(template, self.sample_data)
        self.assertIn('35.5', result)

    def test_element_content_rendering(self):
        """Test element content rendering."""
        element = {
            'element_type': 'text',
            'text_properties': {
                'content': 'Hello, {{name}}!'
            }
        }

        rendered_element = self.renderer.render_element_content(element, self.sample_data)
        self.assertEqual(
            rendered_element['text_properties']['content'],
            'Hello, John Doe!'
        )

    def test_condition_evaluation(self):
        """Test condition evaluation."""
        result = self.renderer.evaluate_condition('total > 30', self.sample_data)
        self.assertTrue(result)

        result = self.renderer.evaluate_condition('total < 30', self.sample_data)
        self.assertFalse(result)

    def test_template_variables_extraction(self):
        """Test template variable extraction."""
        template = 'Hello, {{name}}! Your total is {{total}}.'
        variables = self.renderer.get_template_variables(template)
        self.assertIn('name', variables)
        self.assertIn('total', variables)


class PDFEngineTest(TestCase):
    """Test cases for PDF engines."""

    def setUp(self):
        """Set up test data."""
        self.template_data = {
            'name': 'Test Template',
            'description': 'Test template for engine testing',
            'page_size': 'A4',
            'orientation': 'portrait',
            'elements': [
                {
                    'name': 'Title',
                    'element_type': 'text',
                    'x': 10,
                    'y': 10,
                    'width': 100,
                    'height': 20,
                    'text_properties': {
                        'content': 'Hello, {{name}}!',
                        'font_family': 'Arial',
                        'font_size': 16
                    }
                },
                {
                    'name': 'Items Table',
                    'element_type': 'table',
                    'x': 10,
                    'y': 50,
                    'width': 180,
                    'height': 100,
                    'table_properties': {
                        'columns': [
                            {'name': 'item', 'header': 'Item', 'data_source': 'name'},
                            {'name': 'price', 'header': 'Price', 'data_source': 'price'}
                        ],
                        'data_source': 'items'
                    }
                }
            ]
        }

    def test_supported_languages(self):
        """Test supported languages list."""
        languages = get_supported_languages()
        self.assertIn('python', languages)
        self.assertIn('javascript', languages)
        self.assertIn('php', languages)

    def test_get_engine(self):
        """Test engine retrieval."""
        python_engine = get_engine('python')
        self.assertEqual(python_engine.language, 'python')
        self.assertEqual(python_engine.file_extension, '.py')

        js_engine = get_engine('javascript')
        self.assertEqual(js_engine.language, 'javascript')
        self.assertEqual(js_engine.file_extension, '.js')

        php_engine = get_engine('php')
        self.assertEqual(php_engine.language, 'php')
        self.assertEqual(php_engine.file_extension, '.php')

    def test_invalid_language(self):
        """Test invalid language handling."""
        with self.assertRaises(ValueError):
            get_engine('invalid_language')

    def test_python_engine_validation(self):
        """Test Python engine template validation."""
        engine = get_engine('python')
        errors = engine.validate_template(self.template_data)
        self.assertEqual(len(errors), 0)

        # Test validation with missing required fields
        invalid_template = self.template_data.copy()
        invalid_template['elements'][0]['text_properties']['content'] = ''
        errors = engine.validate_template(invalid_template)
        self.assertGreater(len(errors), 0)

    def test_python_engine_dependencies(self):
        """Test Python engine dependency detection."""
        engine = get_engine('python')
        dependencies = engine.get_dependencies(self.template_data)

        self.assertIn('fpdf2>=2.7.0', dependencies)
        self.assertIn('Pillow>=9.0.0', dependencies)
        self.assertIn('jinja2>=3.1.0', dependencies)  # Due to template content

    def test_python_engine_code_generation(self):
        """Test Python engine code generation."""
        engine = get_engine('python')
        files = engine.generate_code(self.template_data)

        # Check generated files
        self.assertIn('test_template.py', files)
        self.assertIn('__init__.py', files)
        self.assertIn('requirements.txt', files)
        self.assertIn('setup.py', files)
        self.assertIn('README.md', files)

        # Check main module content
        main_module = files['test_template.py']
        self.assertIn('class TestTemplate:', main_module)
        self.assertIn('def generate_pdf(', main_module)
        self.assertIn('from fpdf import FPDF', main_module)

    def test_javascript_engine_code_generation(self):
        """Test JavaScript engine code generation."""
        engine = get_engine('javascript')
        files = engine.generate_code(self.template_data)

        # Check generated files
        self.assertIn('test_template.js', files)
        self.assertIn('test_template.mjs', files)
        self.assertIn('package.json', files)
        self.assertIn('browser.js', files)
        self.assertIn('example.js', files)
        self.assertIn('example.html', files)

        # Check main module content
        main_module = files['test_template.js']
        self.assertIn('class TestTemplate', main_module)
        self.assertIn('generatePDF(', main_module)
        self.assertIn('jsPDF', main_module)

    def test_php_engine_code_generation(self):
        """Test PHP engine code generation."""
        engine = get_engine('php')
        files = engine.generate_code(self.template_data)

        # Check generated files
        self.assertIn('src/TestTemplate.php', files)
        self.assertIn('composer.json', files)
        self.assertIn('README.md', files)

        # Check main class content
        main_class = files['src/TestTemplate.php']
        self.assertIn('class TestTemplate', main_class)
        self.assertIn('public function generatePDF(', main_class)
        self.assertIn('use TCPDF;', main_class)

    def test_engine_package_creation(self):
        """Test engine package creation."""
        engine = get_engine('python')
        package_content = engine.create_package(self.template_data)

        self.assertIsInstance(package_content, bytes)
        self.assertGreater(len(package_content), 0)

        # Verify it's a valid ZIP file
        import zipfile
        import io

        with zipfile.ZipFile(io.BytesIO(package_content), 'r') as zip_file:
            file_list = zip_file.namelist()
            self.assertIn('test_template.py', file_list)
            self.assertIn('requirements.txt', file_list)


class IntegrationTest(TransactionTestCase):
    """Integration tests for the complete PDF generation workflow."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create a complete template with elements
        self.template = PDFTemplate.objects.create(
            name='Invoice Template',
            description='A complete invoice template',
            created_by=self.user,
            page_size=PDFTemplate.PageSize.A4,
            orientation=PDFTemplate.Orientation.PORTRAIT
        )

        # Add text element
        title_element = PDFElement.objects.create(
            template=self.template,
            name='Invoice Title',
            element_type=PDFElement.ElementType.TEXT,
            x=10,
            y=10,
            width=180,
            height=20,
            order=1
        )

        TextElement.objects.create(
            element=title_element,
            content='INVOICE #{{invoice_number}}',
            font_family='Arial',
            font_size=18,
            font_weight=TextElement.FontWeight.BOLD
        )

        # Add table element
        table_element = PDFElement.objects.create(
            template=self.template,
            name='Items Table',
            element_type=PDFElement.ElementType.TABLE,
            x=10,
            y=50,
            width=180,
            height=100,
            order=2
        )

        TableElement.objects.create(
            element=table_element,
            columns=[
                {
                    'name': 'description',
                    'header': 'Description',
                    'width': 100,
                    'data_source': 'description'
                },
                {
                    'name': 'quantity',
                    'header': 'Qty',
                    'width': 30,
                    'data_source': 'quantity'
                },
                {
                    'name': 'price',
                    'header': 'Price',
                    'width': 50,
                    'data_source': 'price'
                }
            ],
            data_source='items',
            show_header=True
        )

        self.service = PDFGenerationService()

    def test_complete_workflow(self):
        """Test complete PDF generation workflow."""
        # 1. Validate template
        is_valid, errors = self.service.validate_template(self.template)
        self.assertTrue(is_valid, f"Template validation failed: {errors}")

        # 2. Generate code for multiple languages
        languages = ['python', 'javascript', 'php']
        generated_codes = []

        for language in languages:
            with patch('apps.pdf_generation.services.get_engine') as mock_get_engine:
                # Mock engine to avoid actual code generation
                mock_engine = MagicMock()
                mock_engine.create_package.return_value = f'fake {language} package'.encode()
                mock_get_engine.return_value = mock_engine

                generated_code = self.service.generate_code(self.template, language)
                generated_codes.append(generated_code)

                self.assertEqual(generated_code.template, self.template)
                self.assertEqual(generated_code.language, language)
                self.assertEqual(generated_code.status, GeneratedCode.Status.COMPLETED)

        # 3. Create template preview
        sample_data = {
            'invoice_number': 'INV-001',
            'items': [
                {'description': 'Widget A', 'quantity': 2, 'price': 10.00},
                {'description': 'Widget B', 'quantity': 1, 'price': 25.00}
            ]
        }

        with patch('apps.pdf_generation.services.get_engine') as mock_get_engine:
            mock_engine = MagicMock()
            mock_engine.generate_code.return_value = {'main.py': 'fake code'}
            mock_get_engine.return_value = mock_engine

            with patch.object(self.service, '_execute_preview_code') as mock_execute:
                mock_execute.return_value = b'fake pdf content'

                preview = self.service.create_template_preview(self.template, sample_data)

                self.assertEqual(preview.template, self.template)
                self.assertEqual(preview.status, TemplatePreview.PreviewStatus.COMPLETED)
                self.assertEqual(preview.sample_data, sample_data)

        # 4. Create template version
        version = self.service.create_template_version(
            self.template,
            'published',
            'Initial publication'
        )

        self.assertEqual(version.template, self.template)
        self.assertEqual(version.change_type, 'published')
        self.assertTrue(version.is_current)

        # 5. Verify usage statistics
        initial_usage = self.template.usage_count
        self.template.increment_usage()
        self.template.refresh_from_db()
        self.assertEqual(self.template.usage_count, initial_usage + 1)

    def test_template_inheritance_workflow(self):
        """Test template inheritance workflow."""
        # Create parent template
        parent_template = PDFTemplate.objects.create(
            name='Base Invoice Template',
            created_by=self.user,
            margin_top=20,
            margin_bottom=20,
            margin_left=15,
            margin_right=15
        )

        # Create child template
        child_template = PDFTemplate.objects.create(
            name='Company Invoice Template',
            created_by=self.user,
            parent_template=parent_template,
            margin_top=25  # Override parent setting
        )

        # Compile child template
        compilation_service = TemplateCompilationService()
        compiled_data = compilation_service.compile_template(child_template)

        # Verify inheritance
        self.assertEqual(compiled_data['margin_top'], 25)  # From child
        self.assertEqual(compiled_data['margin_bottom'], 20)  # From parent
        self.assertEqual(compiled_data['margin_left'], 15)  # From parent
        self.assertEqual(compiled_data['margin_right'], 15)  # From parent

    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test validation error
        empty_template = PDFTemplate.objects.create(
            name='Empty Template',
            created_by=self.user
        )

        is_valid, errors = self.service.validate_template(empty_template)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)

        # Test code generation with invalid language
        with self.assertRaises(ValueError):
            self.service.generate_code(self.template, 'invalid_language')

        # Test engine error handling
        with patch('apps.pdf_generation.services.get_engine') as mock_get_engine:
            mock_engine = MagicMock()
            mock_engine.create_package.side_effect = EngineError('Test error')
            mock_get_engine.return_value = mock_engine

            with self.assertRaises(EngineError):
                self.service.generate_code(self.template, 'python')
