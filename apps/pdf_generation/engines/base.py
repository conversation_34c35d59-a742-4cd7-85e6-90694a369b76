"""
Abstract base class for PDF generation engines.

This module defines the standard interface and common functionality
for all PDF generation engines in different programming languages.
"""

import os
import tempfile
import zipfile
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json
import logging

from jinja2 import Environment, FileSystemLoader, Template, TemplateError
from django.conf import settings
from django.utils.text import slugify

logger = logging.getLogger(__name__)


class EngineError(Exception):
    """Base exception for engine-related errors."""
    pass


class ValidationError(EngineError):
    """Exception raised when template validation fails."""
    pass


class PDFEngine(ABC):
    """
    Abstract base class for PDF generation engines.
    
    Each engine implementation must provide methods for:
    - Template parsing and validation
    - Code generation for the target language
    - Package creation with proper structure
    - Error handling and logging
    """
    
    def __init__(self):
        self.template_env = self._setup_template_environment()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @property
    @abstractmethod
    def language(self) -> str:
        """Return the target programming language."""
        pass
    
    @property
    @abstractmethod
    def file_extension(self) -> str:
        """Return the primary file extension for this language."""
        pass
    
    @property
    @abstractmethod
    def package_manager(self) -> str:
        """Return the package manager name (e.g., 'pip', 'npm', 'composer')."""
        pass
    
    def _setup_template_environment(self) -> Environment:
        """Setup Jinja2 template environment."""
        template_dir = Path(__file__).parent / 'templates'
        if not template_dir.exists():
            template_dir.mkdir(parents=True)
        
        env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        
        # Add custom filters
        env.filters['slugify'] = slugify
        env.filters['camelcase'] = self._to_camel_case
        env.filters['pascalcase'] = self._to_pascal_case
        env.filters['snakecase'] = self._to_snake_case
        
        return env
    
    @staticmethod
    def _to_camel_case(text: str) -> str:
        """Convert text to camelCase."""
        components = text.replace('-', '_').split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    @staticmethod
    def _to_pascal_case(text: str) -> str:
        """Convert text to PascalCase."""
        components = text.replace('-', '_').split('_')
        return ''.join(word.capitalize() for word in components)
    
    @staticmethod
    def _to_snake_case(text: str) -> str:
        """Convert text to snake_case."""
        return text.replace('-', '_').lower()
    
    def validate_template(self, template_data: Dict[str, Any]) -> List[str]:
        """
        Validate template configuration and elements.
        
        Args:
            template_data: Template configuration dictionary
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Basic validation
        if not template_data.get('name'):
            errors.append("Template name is required")
        
        if not template_data.get('elements'):
            errors.append("Template must have at least one element")
        
        # Validate elements
        elements = template_data.get('elements', [])
        for i, element in enumerate(elements):
            element_errors = self._validate_element(element, i)
            errors.extend(element_errors)
        
        return errors
    
    def _validate_element(self, element: Dict[str, Any], index: int) -> List[str]:
        """Validate a single template element."""
        errors = []
        prefix = f"Element {index + 1}"
        
        if not element.get('element_type'):
            errors.append(f"{prefix}: Element type is required")
        
        if not element.get('name'):
            errors.append(f"{prefix}: Element name is required")
        
        # Type-specific validation
        element_type = element.get('element_type')
        if element_type == 'text':
            if not element.get('text_properties', {}).get('content'):
                errors.append(f"{prefix}: Text content is required")
        elif element_type == 'image':
            image_props = element.get('image_properties', {})
            if not any([
                image_props.get('image_url'),
                image_props.get('image_file'),
                image_props.get('image_data_source')
            ]):
                errors.append(f"{prefix}: Image source is required")
        elif element_type == 'table':
            table_props = element.get('table_properties', {})
            if not table_props.get('columns'):
                errors.append(f"{prefix}: Table columns are required")
            if not table_props.get('data_source'):
                errors.append(f"{prefix}: Table data source is required")
        
        return errors
    
    @abstractmethod
    def generate_code(self, template_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Generate code files for the template.
        
        Args:
            template_data: Template configuration dictionary
            options: Engine-specific options
            
        Returns:
            Dictionary mapping file paths to file contents
            
        Raises:
            ValidationError: If template validation fails
            EngineError: If code generation fails
        """
        pass
    
    @abstractmethod
    def get_dependencies(self, template_data: Dict[str, Any]) -> List[str]:
        """
        Get list of dependencies required for the generated code.
        
        Args:
            template_data: Template configuration dictionary
            
        Returns:
            List of dependency specifications
        """
        pass
    
    def create_package(self, template_data: Dict[str, Any], options: Dict[str, Any] = None) -> bytes:
        """
        Create a complete package (ZIP file) with generated code.
        
        Args:
            template_data: Template configuration dictionary
            options: Engine-specific options
            
        Returns:
            ZIP file contents as bytes
            
        Raises:
            ValidationError: If template validation fails
            EngineError: If package creation fails
        """
        # Validate template
        validation_errors = self.validate_template(template_data)
        if validation_errors:
            raise ValidationError(f"Template validation failed: {'; '.join(validation_errors)}")
        
        try:
            # Generate code files
            files = self.generate_code(template_data, options)
            
            # Create ZIP package
            with tempfile.NamedTemporaryFile() as temp_file:
                with zipfile.ZipFile(temp_file, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_path, content in files.items():
                        zip_file.writestr(file_path, content)
                
                temp_file.seek(0)
                return temp_file.read()
        
        except Exception as e:
            self.logger.error(f"Package creation failed: {e}")
            raise EngineError(f"Failed to create package: {e}")
    
    def optimize_code(self, code: str, optimization_level: int = 1) -> str:
        """
        Apply code optimizations.
        
        Args:
            code: Source code to optimize
            optimization_level: Optimization level (1-3)
            
        Returns:
            Optimized code
        """
        # Base implementation - subclasses can override
        if optimization_level >= 2:
            # Remove excessive whitespace
            lines = code.split('\n')
            optimized_lines = []
            for line in lines:
                stripped = line.rstrip()
                if stripped or (optimized_lines and optimized_lines[-1].strip()):
                    optimized_lines.append(stripped)
            code = '\n'.join(optimized_lines)
        
        return code
    
    def get_template_context(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build template context for code generation.
        
        Args:
            template_data: Template configuration dictionary
            
        Returns:
            Template context dictionary
        """
        return {
            'template': template_data,
            'language': self.language,
            'package_manager': self.package_manager,
            'dependencies': self.get_dependencies(template_data),
            'class_name': self._to_pascal_case(template_data.get('name', 'PDFGenerator')),
            'module_name': self._to_snake_case(template_data.get('name', 'pdf_generator')),
            'package_name': slugify(template_data.get('name', 'pdf-generator')),
        }

    def render_template_file(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render a template file with the given context.

        Args:
            template_name: Name of the template file
            context: Template context variables

        Returns:
            Rendered template content

        Raises:
            TemplateError: If template rendering fails
        """
        try:
            template = self.template_env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            self.logger.error(f"Failed to render template '{template_name}': {e}")
            raise EngineError(f"Template rendering failed: {e}")
