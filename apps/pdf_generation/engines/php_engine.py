"""
PHP PDF generation engine.

Generates PHP code using TCPDF library with Composer package structure
and <PERSON><PERSON> service provider for easy integration.
"""

from typing import Dict, List, Any
from .base import PDFEngine, EngineError


class PHPEngine(PDFEngine):
    """
    PHP code generation engine for PDF templates.
    
    Generates PHP packages with:
    - TCPDF library integration
    - Composer package structure with composer.json
    - <PERSON><PERSON> service provider for easy integration
    - PSR-4 autoloading compliance
    """
    
    @property
    def language(self) -> str:
        return 'php'
    
    @property
    def file_extension(self) -> str:
        return '.php'
    
    @property
    def package_manager(self) -> str:
        return 'composer'
    
    def get_dependencies(self, template_data: Dict[str, Any]) -> List[str]:
        """Get PHP dependencies based on template features."""
        dependencies = [
            'tecnickcom/tcpdf:^6.6',
        ]
        
        # Check for additional features
        elements = template_data.get('elements', [])
        
        for element in elements:
            element_type = element.get('element_type')
            
            # Add image processing support
            if element_type == 'image':
                dependencies.append('intervention/image:^2.7')
            
            # Add template engine if needed
            elif element_type == 'text':
                text_props = element.get('text_properties', {})
                if '{{' in str(text_props.get('content', '')):
                    dependencies.append('twig/twig:^3.0')
        
        return dependencies
    
    def generate_code(self, template_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate PHP code files for the template."""
        options = options or {}
        context = self.get_template_context(template_data)
        
        files = {}
        
        # Generate main class
        files[f"src/{context['class_name']}.php"] = self._generate_main_class(context)
        
        # Generate package files
        files['composer.json'] = self._generate_composer_json(context)
        files['README.md'] = self._generate_readme(context)
        
        # Generate Laravel service provider
        files[f"src/Laravel/{context['class_name']}ServiceProvider.php"] = self._generate_laravel_provider(context)
        
        # Generate utility classes
        files['src/Utils/DataResolver.php'] = self._generate_data_resolver(context)
        files['src/Utils/ColorHelper.php'] = self._generate_color_helper(context)
        
        # Generate example files
        files['examples/basic_usage.php'] = self._generate_example(context)
        files['examples/laravel_usage.php'] = self._generate_laravel_example(context)
        
        # Apply optimizations
        optimization_level = options.get('optimization_level', 1)
        if optimization_level > 1:
            for file_path, content in files.items():
                if file_path.endswith('.php'):
                    files[file_path] = self.optimize_code(content, optimization_level)
        
        return files
    
    def _generate_main_class(self, context: Dict[str, Any]) -> str:
        """Generate the main PDF generator class."""
        template_data = context['template']
        class_name = context['class_name']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))
        
        # Determine which libraries to import
        needs_twig = 'twig/twig' in context['dependencies']
        needs_intervention = 'intervention/image' in context['dependencies']
        
        code = f'''<?php

namespace {namespace};

use TCPDF;
'''
        
        if needs_twig:
            code += '''use Twig\\Environment;
use Twig\\Loader\\ArrayLoader;
'''
        
        if needs_intervention:
            code += '''use Intervention\\Image\\ImageManagerStatic as Image;
'''
        
        code += f'''

/**
 * {template_data.get('description', f'PDF Generator for {template_data.get("name", "Template")}')}
 * 
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */
class {class_name}
{{
    /**
     * Template configuration
     * @var array
     */
    private $templateConfig;
    
    /**
     * Page dimensions
     * @var array
     */
    private $pageDimensions;
    
    /**
     * TCPDF instance
     * @var TCPDF
     */
    private $pdf;
    
    /**
     * Twig environment for template rendering
     * @var Environment|null
     */
    private $twig;
    
    /**
     * Constructor
     */
    public function __construct()
    {{
        $this->templateConfig = {self._format_template_config_php(template_data)};
        $this->pageDimensions = $this->getPageDimensions();
        $this->initializeTwig();
    }}
    
    /**
     * Get page dimensions based on template configuration
     * @return array
     */
    private function getPageDimensions(): array
    {{
        $config = $this->templateConfig;
        $pageSize = $config['page_size'] ?? 'A4';
        $orientation = $config['orientation'] ?? 'portrait';
        
        // Standard page sizes in mm
        $sizes = [
            'A4' => ['width' => 210, 'height' => 297],
            'A3' => ['width' => 297, 'height' => 420],
            'A5' => ['width' => 148, 'height' => 210],
            'LETTER' => ['width' => 215.9, 'height' => 279.4],
            'LEGAL' => ['width' => 215.9, 'height' => 355.6],
        ];
        
        if ($pageSize === 'CUSTOM') {{
            $dimensions = [
                'width' => $config['custom_width'] ?? 210,
                'height' => $config['custom_height'] ?? 297
            ];
        }} else {{
            $dimensions = $sizes[$pageSize] ?? $sizes['A4'];
        }}
        
        if ($orientation === 'landscape') {{
            return ['width' => $dimensions['height'], 'height' => $dimensions['width']];
        }}
        
        return $dimensions;
    }}
    
    /**
     * Initialize Twig environment for template rendering
     */
    private function initializeTwig(): void
    {{
        if (class_exists('Twig\\Environment')) {{
            $loader = new ArrayLoader([]);
            $this->twig = new Environment($loader);
        }}
    }}
    
    /**
     * Generate PDF document with provided data
     * @param array $data Data context for template rendering
     * @param string|null $outputPath Optional output file path
     * @return string PDF content
     */
    public function generatePDF(array $data, ?string $outputPath = null): string
    {{
        // Initialize TCPDF
        $orientation = $this->pageDimensions['width'] < $this->pageDimensions['height'] ? 'P' : 'L';
        $this->pdf = new TCPDF($orientation, 'mm', [$this->pageDimensions['width'], $this->pageDimensions['height']]);
        
        // Set document information
        $this->pdf->SetCreator('PDFlex Generator');
        $this->pdf->SetAuthor('Generated by PDFlex');
        $this->pdf->SetTitle($this->templateConfig['name'] ?? 'Generated Document');
        
        // Set margins
        $margins = $this->templateConfig;
        $this->pdf->SetMargins(
            $margins['margin_left'] ?? 20,
            $margins['margin_top'] ?? 20,
            $margins['margin_right'] ?? 20
        );
        $this->pdf->SetAutoPageBreak(true, $margins['margin_bottom'] ?? 20);
        
        // Add page
        $this->pdf->AddPage();
        
        // Process template elements
        $elements = $this->templateConfig['elements'] ?? [];
        foreach ($elements as $element) {{
            $this->renderElement($element, $data);
        }}
        
        // Output PDF
        if ($outputPath) {{
            $this->pdf->Output($outputPath, 'F');
            return file_get_contents($outputPath);
        }} else {{
            return $this->pdf->Output('', 'S');
        }}
    }}
    
    /**
     * Render a single template element
     * @param array $element Element configuration
     * @param array $data Data context
     */
    private function renderElement(array $element, array $data): void
    {{
        if (isset($element['visible']) && !$element['visible']) {{
            return;
        }}
        
        $elementType = $element['element_type'] ?? '';
        
        switch ($elementType) {{
            case 'text':
                $this->renderTextElement($element, $data);
                break;
            case 'image':
                $this->renderImageElement($element, $data);
                break;
            case 'table':
                $this->renderTableElement($element, $data);
                break;
        }}
    }}
    
    /**
     * Render text element
     * @param array $element Element configuration
     * @param array $data Data context
     */
    private function renderTextElement(array $element, array $data): void
    {{
        $textProps = $element['text_properties'] ?? [];
        $content = $textProps['content'] ?? '';
        
        // Render template content
        if ($this->twig && strpos($content, '{{{{') !== false) {{
            try {{
                $template = $this->twig->createTemplate($content);
                $content = $template->render($data);
            }} catch (Exception $e) {{
                $content = "[Template Error: " . $e->getMessage() . "]";
            }}
        }}
        
        // Set font
        $fontFamily = $textProps['font_family'] ?? 'helvetica';
        $fontSize = $textProps['font_size'] ?? 12;
        $fontWeight = $textProps['font_weight'] ?? 'normal';
        
        $fontStyle = '';
        if ($fontWeight === 'bold') {{
            $fontStyle .= 'B';
        }}
        if (($textProps['font_style'] ?? 'normal') === 'italic') {{
            $fontStyle .= 'I';
        }}
        if ($textProps['underline'] ?? false) {{
            $fontStyle .= 'U';
        }}
        
        $this->pdf->SetFont($fontFamily, $fontStyle, $fontSize);
        
        // Set text color
        $textColor = $textProps['text_color'] ?? '#000000';
        $rgb = $this->hexToRgb($textColor);
        $this->pdf->SetTextColor($rgb['r'], $rgb['g'], $rgb['b']);
        
        // Set position
        $x = $element['x'] ?? 0;
        $y = $element['y'] ?? 0;
        $width = $element['width'] ?? 0;
        $height = $element['height'] ?? 0;
        
        // Render text
        $align = strtoupper(substr($textProps['text_align'] ?? 'left', 0, 1));
        
        if ($width > 0 && $height > 0) {{
            $this->pdf->SetXY($x, $y);
            $this->pdf->Cell($width, $height, $content, 0, 0, $align);
        }} else {{
            $this->pdf->Text($x, $y, $content);
        }}
    }}
    
    /**
     * Render image element
     * @param array $element Element configuration
     * @param array $data Data context
     */
    private function renderImageElement(array $element, array $data): void
    {{
        $imageProps = $element['image_properties'] ?? [];
        
        // Get image source
        $imageUrl = $imageProps['image_url'] ?? null;
        if (!$imageUrl && isset($imageProps['image_data_source'])) {{
            $imageUrl = $this->resolveDataPath($imageProps['image_data_source'], $data);
        }}
        
        if ($imageUrl) {{
            $x = $element['x'] ?? 0;
            $y = $element['y'] ?? 0;
            $width = $element['width'] ?? 50;
            $height = $element['height'] ?? 50;
            
            try {{
                $this->pdf->Image($imageUrl, $x, $y, $width, $height);
            }} catch (Exception $e) {{
                // Fallback: render placeholder text
                $this->pdf->SetXY($x, $y);
                $this->pdf->Cell($width, $height, "[Image Error: " . $e->getMessage() . "]", 1);
            }}
        }}
    }}
    
    /**
     * Render table element
     * @param array $element Element configuration
     * @param array $data Data context
     */
    private function renderTableElement(array $element, array $data): void
    {{
        $tableProps = $element['table_properties'] ?? [];
        $columns = $tableProps['columns'] ?? [];
        $dataSource = $tableProps['data_source'] ?? null;
        
        if (empty($columns) || !$dataSource) {{
            return;
        }}
        
        // Get table data
        $tableData = $this->resolveDataPath($dataSource, $data);
        if (!is_array($tableData)) {{
            return;
        }}
        
        // Basic table rendering
        $x = $element['x'] ?? 0;
        $y = $element['y'] ?? 0;
        $totalWidth = $element['width'] ?? 100;
        $cellHeight = 8;
        $cellWidth = $totalWidth / count($columns);
        
        $this->pdf->SetXY($x, $y);
        
        // Render header
        if ($tableProps['show_header'] ?? true) {{
            $this->pdf->SetFont('helvetica', 'B', 10);
            $headerBg = $this->hexToRgb($tableProps['header_background_color'] ?? '#f0f0f0');
            $this->pdf->SetFillColor($headerBg['r'], $headerBg['g'], $headerBg['b']);
            
            foreach ($columns as $col) {{
                $this->pdf->Cell($cellWidth, $cellHeight, $col['header'] ?? $col['name'] ?? '', 1, 0, 'C', true);
            }}
            $this->pdf->Ln();
        }}
        
        // Render data rows
        $this->pdf->SetFont('helvetica', '', 10);
        $this->pdf->SetFillColor(255, 255, 255);
        
        foreach (array_slice($tableData, 0, 20) as $row) {{
            foreach ($columns as $col) {{
                $value = $this->resolveDataPath($col['data_source'] ?? $col['name'] ?? '', ['row' => $row]);
                $this->pdf->Cell($cellWidth, $cellHeight, (string)($value ?? ''), 1);
            }}
            $this->pdf->Ln();
        }}
    }}
    
    /**
     * Convert hex color to RGB array
     * @param string $hex Hex color string
     * @return array RGB color array
     */
    private function hexToRgb(string $hex): array
    {{
        $hex = ltrim($hex, '#');
        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }}
    
    /**
     * Resolve dot-notation data path
     * @param string $path Dot-notation path
     * @param array $data Data array
     * @return mixed Resolved value or null
     */
    private function resolveDataPath(string $path, array $data)
    {{
        $parts = explode('.', $path);
        $value = $data;
        
        foreach ($parts as $part) {{
            if (is_array($value) && array_key_exists($part, $value)) {{
                $value = $value[$part];
            }} else {{
                return null;
            }}
        }}
        
        return $value;
    }}
}}
'''
        
        return code
    
    def _format_template_config_php(self, template_data: Dict[str, Any]) -> str:
        """Format template configuration as PHP array."""
        import json
        json_str = json.dumps(template_data, indent=8)
        # Convert JSON to PHP array syntax
        php_array = json_str.replace('{', '[').replace('}', ']').replace(':', ' =>')
        return php_array

    def _generate_composer_json(self, context: Dict[str, Any]) -> str:
        """Generate composer.json file."""
        template_data = context['template']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        # Parse dependencies
        require = {}
        for dep in context['dependencies']:
            if ':' in dep:
                name, version = dep.split(':', 1)
                require[name] = f"^{version}"
            else:
                require[dep] = "*"

        require['php'] = ">=7.4"

        composer_json = {
            "name": context['package_name'],
            "description": template_data.get('description', 'PDF Generator'),
            "type": "library",
            "license": "MIT",
            "authors": [
                {
                    "name": "Generated by PDFlex",
                    "email": "<EMAIL>"
                }
            ],
            "require": require,
            "autoload": {
                "psr-4": {
                    f"{namespace}\\": "src/"
                }
            },
            "autoload-dev": {
                "psr-4": {
                    f"{namespace}\\Tests\\": "tests/"
                }
            },
            "minimum-stability": "stable",
            "prefer-stable": True
        }

        import json
        return json.dumps(composer_json, indent=4)

    def _generate_laravel_provider(self, context: Dict[str, Any]) -> str:
        """Generate Laravel service provider."""
        class_name = context['class_name']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''<?php

namespace {namespace}\\Laravel;

use Illuminate\\Support\\ServiceProvider;
use {namespace}\\{class_name};

/**
 * Laravel Service Provider for {class_name}
 *
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */
class {class_name}ServiceProvider extends ServiceProvider
{{
    /**
     * Register services.
     */
    public function register(): void
    {{
        $this->app->singleton({class_name}::class, function ($app) {{
            return new {class_name}();
        }});

        $this->app->alias({class_name}::class, 'pdf-generator');
    }}

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {{
        // Publish configuration file
        $this->publishes([
            __DIR__ . '/../config/pdf-generator.php' => config_path('pdf-generator.php'),
        ], 'config');

        // Register Blade directive for PDF generation
        \\Illuminate\\Support\\Facades\\Blade::directive('pdf', function ($expression) {{
            return "<?php echo app('{class_name}')->generatePDF($expression); ?>";
        }});
    }}

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {{
        return [{class_name}::class, 'pdf-generator'];
    }}
}}
'''

    def _generate_data_resolver(self, context: Dict[str, Any]) -> str:
        """Generate data resolver utility class."""
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''<?php

namespace {namespace}\\Utils;

/**
 * Data resolver utility for handling dot-notation paths
 *
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */
class DataResolver
{{
    /**
     * Resolve dot-notation data path
     * @param string $path Dot-notation path (e.g., 'user.profile.name')
     * @param array $data Data array
     * @param mixed $default Default value if path not found
     * @return mixed Resolved value or default
     */
    public static function resolve(string $path, array $data, $default = null)
    {{
        $parts = explode('.', $path);
        $value = $data;

        foreach ($parts as $part) {{
            if (is_array($value) && array_key_exists($part, $value)) {{
                $value = $value[$part];
            }} elseif (is_object($value) && property_exists($value, $part)) {{
                $value = $value->$part;
            }} else {{
                return $default;
            }}
        }}

        return $value;
    }}

    /**
     * Check if a dot-notation path exists in data
     * @param string $path Dot-notation path
     * @param array $data Data array
     * @return bool True if path exists
     */
    public static function exists(string $path, array $data): bool
    {{
        $parts = explode('.', $path);
        $value = $data;

        foreach ($parts as $part) {{
            if (is_array($value) && array_key_exists($part, $value)) {{
                $value = $value[$part];
            }} elseif (is_object($value) && property_exists($value, $part)) {{
                $value = $value->$part;
            }} else {{
                return false;
            }}
        }}

        return true;
    }}

    /**
     * Set value at dot-notation path
     * @param string $path Dot-notation path
     * @param array &$data Data array (passed by reference)
     * @param mixed $value Value to set
     */
    public static function set(string $path, array &$data, $value): void
    {{
        $parts = explode('.', $path);
        $current = &$data;

        foreach ($parts as $i => $part) {{
            if ($i === count($parts) - 1) {{
                $current[$part] = $value;
            }} else {{
                if (!isset($current[$part]) || !is_array($current[$part])) {{
                    $current[$part] = [];
                }}
                $current = &$current[$part];
            }}
        }}
    }}

    /**
     * Format value based on type
     * @param mixed $value Value to format
     * @param string $format Format type ('currency', 'date', 'number')
     * @param array $options Format options
     * @return string Formatted value
     */
    public static function format($value, string $format = 'text', array $options = []): string
    {{
        switch ($format) {{
            case 'currency':
                $currency = $options['currency'] ?? 'USD';
                $decimals = $options['decimals'] ?? 2;
                return $currency . ' ' . number_format((float)$value, $decimals);

            case 'date':
                $dateFormat = $options['format'] ?? 'Y-m-d';
                if (is_string($value)) {{
                    $timestamp = strtotime($value);
                    return $timestamp ? date($dateFormat, $timestamp) : $value;
                }}
                return (string)$value;

            case 'number':
                $decimals = $options['decimals'] ?? 0;
                return number_format((float)$value, $decimals);

            default:
                return (string)$value;
        }}
    }}
}}
'''

    def _generate_color_helper(self, context: Dict[str, Any]) -> str:
        """Generate color helper utility class."""
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''<?php

namespace {namespace}\\Utils;

/**
 * Color helper utility for color conversions
 *
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */
class ColorHelper
{{
    /**
     * Convert hex color to RGB array
     * @param string $hex Hex color string (with or without #)
     * @return array RGB color array with 'r', 'g', 'b' keys
     */
    public static function hexToRgb(string $hex): array
    {{
        $hex = ltrim($hex, '#');

        if (strlen($hex) === 3) {{
            $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
        }}

        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }}

    /**
     * Convert RGB values to hex color
     * @param int $r Red value (0-255)
     * @param int $g Green value (0-255)
     * @param int $b Blue value (0-255)
     * @return string Hex color string with #
     */
    public static function rgbToHex(int $r, int $g, int $b): string
    {{
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }}

    /**
     * Convert hex color to CMYK array
     * @param string $hex Hex color string
     * @return array CMYK color array with 'c', 'm', 'y', 'k' keys
     */
    public static function hexToCmyk(string $hex): array
    {{
        $rgb = self::hexToRgb($hex);

        $r = $rgb['r'] / 255;
        $g = $rgb['g'] / 255;
        $b = $rgb['b'] / 255;

        $k = 1 - max($r, $g, $b);

        if ($k == 1) {{
            return ['c' => 0, 'm' => 0, 'y' => 0, 'k' => 100];
        }}

        $c = (1 - $r - $k) / (1 - $k);
        $m = (1 - $g - $k) / (1 - $k);
        $y = (1 - $b - $k) / (1 - $k);

        return [
            'c' => round($c * 100),
            'm' => round($m * 100),
            'y' => round($y * 100),
            'k' => round($k * 100)
        ];
    }}

    /**
     * Validate hex color format
     * @param string $hex Hex color string
     * @return bool True if valid hex color
     */
    public static function isValidHex(string $hex): bool
    {{
        $hex = ltrim($hex, '#');
        return ctype_xdigit($hex) && (strlen($hex) === 3 || strlen($hex) === 6);
    }}

    /**
     * Get contrasting color (black or white) for given hex color
     * @param string $hex Hex color string
     * @return string Contrasting hex color (#000000 or #ffffff)
     */
    public static function getContrastColor(string $hex): string
    {{
        $rgb = self::hexToRgb($hex);

        // Calculate luminance
        $luminance = (0.299 * $rgb['r'] + 0.587 * $rgb['g'] + 0.114 * $rgb['b']) / 255;

        return $luminance > 0.5 ? '#000000' : '#ffffff';
    }}
}}
'''

    def _generate_readme(self, context: Dict[str, Any]) -> str:
        """Generate README.md file."""
        template_data = context['template']
        class_name = context['class_name']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''# {template_data.get('name', 'PDF Generator')}

{template_data.get('description', 'PDF Generator created with PDFlex')}

## Installation

Install via Composer:

```bash
composer install
```

## Usage

### Basic Usage

```php
<?php

require_once 'vendor/autoload.php';

use {namespace}\\{class_name};

// Create generator instance
$generator = new {class_name}();

// Sample data
$data = [
    'title' => 'Sample Document',
    'content' => 'This is a sample PDF document.',
    'items' => [
        ['name' => 'Item 1', 'value' => 100],
        ['name' => 'Item 2', 'value' => 200],
    ]
];

// Generate PDF
$pdfContent = $generator->generatePDF($data);

// Save to file
file_put_contents('output.pdf', $pdfContent);

// Or output directly to browser
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="document.pdf"');
echo $pdfContent;
```

### Laravel Integration

Add the service provider to your `config/app.php`:

```php
'providers' => [
    // ...
    {namespace}\\Laravel\\{class_name}ServiceProvider::class,
],
```

Then use in your Laravel application:

```php
<?php

use {namespace}\\{class_name};

class DocumentController extends Controller
{{
    public function generatePdf(Request $request)
    {{
        $generator = app({class_name}::class);

        $data = [
            'user' => $request->user(),
            'items' => $request->input('items', []),
        ];

        $pdfContent = $generator->generatePDF($data);

        return response($pdfContent)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="document.pdf"');
    }}
}}
```

### Using Blade Directive

```blade
{{-- In your Blade template --}}
@pdf($data)
```

## API Reference

### Class: {class_name}

#### Methods

- `__construct()` - Create new PDF generator instance
- `generatePDF(array $data, ?string $outputPath = null): string` - Generate PDF with provided data
  - `$data` (array) - Data context for template rendering
  - `$outputPath` (string|null) - Optional output file path
  - Returns: PDF content as string

### Utility Classes

#### DataResolver

Helper class for resolving dot-notation data paths:

```php
use {namespace}\\Utils\\DataResolver;

$value = DataResolver::resolve('user.profile.name', $data, 'Default Name');
$exists = DataResolver::exists('user.email', $data);
DataResolver::set('user.status', $data, 'active');
$formatted = DataResolver::format($value, 'currency', ['currency' => 'USD']);
```

#### ColorHelper

Helper class for color conversions:

```php
use {namespace}\\Utils\\ColorHelper;

$rgb = ColorHelper::hexToRgb('#ff0000');
$hex = ColorHelper::rgbToHex(255, 0, 0);
$cmyk = ColorHelper::hexToCmyk('#ff0000');
$isValid = ColorHelper::isValidHex('#ff0000');
$contrast = ColorHelper::getContrastColor('#ff0000');
```

## Requirements

- PHP >= 7.4
- TCPDF library
- Composer for dependency management

## Generated by PDFlex

This package was automatically generated by [PDFlex](https://github.com/your-org/pdflex) - Universal Form-to-PDF Generator.
'''

    def _generate_example(self, context: Dict[str, Any]) -> str:
        """Generate basic usage example."""
        template_data = context['template']
        class_name = context['class_name']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''<?php
/**
 * Basic usage example for {template_data.get('name', 'PDF Generator')}
 *
 * This script demonstrates how to use the generated PDF generator
 * with sample data.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use {namespace}\\{class_name};

try {{
    // Create generator instance
    $generator = new {class_name}();

    // Sample data - customize this based on your template
    $sampleData = [
        'title' => 'Sample Document',
        'date' => date('Y-m-d'),
        'content' => 'This is a sample PDF document generated by PDFlex.',
        'items' => [
            [
                'name' => 'Item 1',
                'description' => 'First item',
                'quantity' => 2,
                'price' => 10.00
            ],
            [
                'name' => 'Item 2',
                'description' => 'Second item',
                'quantity' => 1,
                'price' => 25.00
            ],
            [
                'name' => 'Item 3',
                'description' => 'Third item',
                'quantity' => 3,
                'price' => 5.00
            ],
        ],
        'total' => 55.00,
        'customer' => [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'address' => '123 Main St, City, State 12345'
        ]
    ];

    // Generate PDF
    echo "Generating PDF...\\n";
    $pdfContent = $generator->generatePDF($sampleData);

    // Save to file
    $outputFile = __DIR__ . '/sample_output.pdf';
    file_put_contents($outputFile, $pdfContent);

    echo "PDF generated successfully: $outputFile\\n";
    echo "File size: " . strlen($pdfContent) . " bytes\\n";

}} catch (Exception $e) {{
    echo "Error generating PDF: " . $e->getMessage() . "\\n";
    exit(1);
}}
'''

    def _generate_laravel_example(self, context: Dict[str, Any]) -> str:
        """Generate Laravel usage example."""
        template_data = context['template']
        class_name = context['class_name']
        namespace = self._to_pascal_case(context['package_name'].replace('-', '_'))

        return f'''<?php
/**
 * Laravel usage example for {template_data.get('name', 'PDF Generator')}
 *
 * This example shows how to integrate the PDF generator
 * with a Laravel application.
 */

namespace App\\Http\\Controllers;

use Illuminate\\Http\\Request;
use Illuminate\\Http\\Response;
use Illuminate\\Support\\Facades\\Storage;
use {namespace}\\{class_name};

class PdfController extends Controller
{{
    /**
     * Generate and download PDF
     */
    public function download(Request $request): Response
    {{
        $generator = app({class_name}::class);

        // Get data from request or database
        $data = [
            'title' => $request->input('title', 'Generated Document'),
            'date' => now()->format('Y-m-d'),
            'user' => $request->user(),
            'items' => $request->input('items', []),
            'total' => $request->input('total', 0),
        ];

        // Generate PDF
        $pdfContent = $generator->generatePDF($data);

        // Return as download
        return response($pdfContent)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="document.pdf"');
    }}

    /**
     * Generate and store PDF
     */
    public function store(Request $request): array
    {{
        $generator = app({class_name}::class);

        $data = $request->validate([
            'title' => 'required|string',
            'items' => 'array',
            'customer' => 'array',
        ]);

        // Generate PDF
        $pdfContent = $generator->generatePDF($data);

        // Store in filesystem
        $filename = 'documents/' . uniqid() . '.pdf';
        Storage::put($filename, $pdfContent);

        return [
            'success' => true,
            'filename' => $filename,
            'size' => strlen($pdfContent),
            'url' => Storage::url($filename),
        ];
    }}

    /**
     * Generate PDF and email it
     */
    public function email(Request $request): array
    {{
        $generator = app({class_name}::class);

        $data = $request->all();
        $pdfContent = $generator->generatePDF($data);

        // Create temporary file
        $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
        file_put_contents($tempFile, $pdfContent);

        // Send email with PDF attachment
        \\Mail::send('emails.pdf-generated', $data, function ($message) use ($tempFile, $data) {{
            $message->to($data['email'] ?? '<EMAIL>')
                    ->subject('Your PDF Document')
                    ->attach($tempFile, [
                        'as' => 'document.pdf',
                        'mime' => 'application/pdf',
                    ]);
        }});

        // Clean up
        unlink($tempFile);

        return [
            'success' => true,
            'message' => 'PDF generated and emailed successfully',
        ];
    }}

    /**
     * Generate PDF preview (base64)
     */
    public function preview(Request $request): array
    {{
        $generator = app({class_name}::class);

        $data = $request->all();
        $pdfContent = $generator->generatePDF($data);

        return [
            'success' => true,
            'preview' => base64_encode($pdfContent),
            'size' => strlen($pdfContent),
        ];
    }}
}}

/**
 * Example routes (add to routes/web.php or routes/api.php)
 */
/*
Route::post('/pdf/download', [PdfController::class, 'download']);
Route::post('/pdf/store', [PdfController::class, 'store']);
Route::post('/pdf/email', [PdfController::class, 'email']);
Route::post('/pdf/preview', [PdfController::class, 'preview']);
*/

/**
 * Example Blade component usage
 */
/*
// In a Blade template:
<form action="/pdf/download" method="POST">
    @csrf
    <input type="text" name="title" placeholder="Document Title">
    <textarea name="content" placeholder="Content"></textarea>
    <button type="submit">Generate PDF</button>
</form>

// Or using the Blade directive:
@pdf([
    'title' => 'My Document',
    'content' => 'Document content here',
    'items' => $items
])
*/
'''
