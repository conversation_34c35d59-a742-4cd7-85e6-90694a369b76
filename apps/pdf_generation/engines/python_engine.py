"""
Python PDF generation engine.

Generates production-ready Python code using FPDF2 and ReportLab libraries
with proper package structure and requirements.txt.
"""

from typing import Dict, List, Any
from .base import PDFEngine, EngineError


class PythonEngine(PDFEngine):
    """
    Python code generation engine for PDF templates.
    
    Generates Python packages with:
    - FPDF2 for basic PDF generation
    - ReportLab for advanced features
    - Proper Python package structure
    - Requirements.txt with pinned versions
    - Setup.py for distribution
    """
    
    @property
    def language(self) -> str:
        return 'python'
    
    @property
    def file_extension(self) -> str:
        return '.py'
    
    @property
    def package_manager(self) -> str:
        return 'pip'
    
    def get_dependencies(self, template_data: Dict[str, Any]) -> List[str]:
        """Get Python dependencies based on template features."""
        dependencies = [
            'fpdf2>=2.7.0',
            'Pillow>=9.0.0',
        ]
        
        # Check if advanced features are needed
        elements = template_data.get('elements', [])
        needs_reportlab = False
        
        for element in elements:
            element_type = element.get('element_type')
            
            # ReportLab needed for complex tables, charts, or advanced graphics
            if element_type == 'table':
                table_props = element.get('table_properties', {})
                if len(table_props.get('columns', [])) > 5:  # Complex tables
                    needs_reportlab = True
            elif element_type == 'shape' or element.get('style', {}).get('border_radius', 0) > 0:
                needs_reportlab = True
        
        if needs_reportlab:
            dependencies.append('reportlab>=4.0.0')
        
        # Add data processing dependencies if needed
        if any(elem.get('data_source') for elem in elements):
            dependencies.append('jinja2>=3.1.0')
        
        return dependencies
    
    def generate_code(self, template_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate Python code files for the template."""
        options = options or {}
        context = self.get_template_context(template_data)
        
        files = {}
        
        # Generate main module
        files[f"{context['module_name']}.py"] = self._generate_main_module(context)
        
        # Generate package files
        files['__init__.py'] = self._generate_init_file(context)
        files['requirements.txt'] = self._generate_requirements(context)
        files['setup.py'] = self._generate_setup_file(context)
        files['README.md'] = self._generate_readme(context)
        
        # Generate utility modules
        files['utils.py'] = self._generate_utils_module(context)
        
        # Generate example usage
        files['example.py'] = self._generate_example(context)
        
        # Apply optimizations
        optimization_level = options.get('optimization_level', 1)
        if optimization_level > 1:
            for file_path, content in files.items():
                if file_path.endswith('.py'):
                    files[file_path] = self.optimize_code(content, optimization_level)
        
        return files
    
    def _generate_main_module(self, context: Dict[str, Any]) -> str:
        """Generate the main PDF generator module."""
        template_data = context['template']
        class_name = context['class_name']
        
        # Determine which libraries to import
        needs_reportlab = 'reportlab' in context['dependencies']
        
        code = f'''"""
{template_data.get('description', f'PDF Generator for {template_data.get("name", "Template")}')}

Generated by PDFlex - Universal Form-to-PDF Generator
"""

import os
from typing import Dict, Any, Optional, List
from fpdf import FPDF
'''
        
        if needs_reportlab:
            code += '''from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.lib import colors
'''
        
        if any('jinja2' in dep for dep in context['dependencies']):
            code += '''from jinja2 import Template
'''
        
        code += f'''

class {class_name}:
    """
    PDF Generator class for {template_data.get('name', 'Template')}.
    
    This class generates PDF documents based on the template configuration
    and provided data context.
    """
    
    def __init__(self):
        """Initialize the PDF generator."""
        self.template_config = {self._format_template_config(template_data)}
        self.page_width, self.page_height = self._get_page_dimensions()
    
    def _get_page_dimensions(self) -> tuple:
        """Get page dimensions based on template configuration."""
        config = self.template_config
        page_size = config.get('page_size', 'A4')
        orientation = config.get('orientation', 'portrait')
        
        # Standard page sizes in mm
        sizes = {{
            'A4': (210, 297),
            'A3': (297, 420),
            'A5': (148, 210),
            'LETTER': (215.9, 279.4),
            'LEGAL': (215.9, 355.6),
        }}
        
        if page_size == 'CUSTOM':
            width = config.get('custom_width', 210)
            height = config.get('custom_height', 297)
        else:
            width, height = sizes.get(page_size, (210, 297))
        
        if orientation == 'landscape':
            width, height = height, width
        
        return width, height
    
    def generate_pdf(self, data: Dict[str, Any], output_path: str = None) -> bytes:
        """
        Generate PDF document with provided data.
        
        Args:
            data: Data context for template rendering
            output_path: Optional output file path
            
        Returns:
            PDF content as bytes
        """
        pdf = FPDF(orientation='P' if self.page_width < self.page_height else 'L',
                   unit='mm', format=(self.page_width, self.page_height))
        
        pdf.add_page()
        
        # Process template elements
        for element in self.template_config.get('elements', []):
            self._render_element(pdf, element, data)
        
        # Save or return PDF
        if output_path:
            pdf.output(output_path)
            with open(output_path, 'rb') as f:
                return f.read()
        else:
            return pdf.output(dest='S').encode('latin1')
    
    def _render_element(self, pdf: FPDF, element: Dict[str, Any], data: Dict[str, Any]):
        """Render a single template element."""
        if not element.get('visible', True):
            return
        
        element_type = element.get('element_type')
        
        if element_type == 'text':
            self._render_text_element(pdf, element, data)
        elif element_type == 'image':
            self._render_image_element(pdf, element, data)
        elif element_type == 'table':
            self._render_table_element(pdf, element, data)
    
    def _render_text_element(self, pdf: FPDF, element: Dict[str, Any], data: Dict[str, Any]):
        """Render text element."""
        text_props = element.get('text_properties', {{}})
        content = text_props.get('content', '')
        
        # Render template content
        if '{{{{' in content and '}}}}' in content:
            template = Template(content)
            content = template.render(**data)
        
        # Set font
        font_family = text_props.get('font_family', 'Arial')
        font_size = text_props.get('font_size', 12)
        font_weight = text_props.get('font_weight', 'normal')
        
        style = 'B' if font_weight == 'bold' else ''
        pdf.set_font(font_family, style, font_size)
        
        # Set position
        x = element.get('x', 0)
        y = element.get('y', 0)
        pdf.set_xy(x, y)
        
        # Set text color
        text_color = text_props.get('text_color', '#000000')
        r, g, b = self._hex_to_rgb(text_color)
        pdf.set_text_color(r, g, b)
        
        # Render text
        width = element.get('width', 0)
        if width > 0:
            pdf.cell(width, font_size * 0.35, content, align=text_props.get('text_align', 'L')[0].upper())
        else:
            pdf.cell(0, font_size * 0.35, content)
    
    def _render_image_element(self, pdf: FPDF, element: Dict[str, Any], data: Dict[str, Any]):
        """Render image element."""
        image_props = element.get('image_properties', {{}})
        
        # Get image source
        image_url = image_props.get('image_url')
        if not image_url and image_props.get('image_data_source'):
            image_url = self._resolve_data_path(image_props['image_data_source'], data)
        
        if image_url:
            x = element.get('x', 0)
            y = element.get('y', 0)
            width = element.get('width', 50)
            height = element.get('height', 50)
            
            try:
                pdf.image(image_url, x, y, width, height)
            except Exception as e:
                # Fallback: render placeholder text
                pdf.set_xy(x, y)
                pdf.cell(width, height, f"[Image Error: {{e}}]", border=1)
    
    def _render_table_element(self, pdf: FPDF, element: Dict[str, Any], data: Dict[str, Any]):
        """Render table element."""
        table_props = element.get('table_properties', {{}})
        columns = table_props.get('columns', [])
        data_source = table_props.get('data_source')
        
        if not columns or not data_source:
            return
        
        # Get table data
        table_data = self._resolve_data_path(data_source, data)
        if not isinstance(table_data, list):
            return
        
        # Basic table rendering with FPDF
        x = element.get('x', 0)
        y = element.get('y', 0)
        pdf.set_xy(x, y)
        
        # Calculate column widths
        total_width = element.get('width', 100)
        col_width = total_width / len(columns)
        row_height = 8
        
        # Render header
        if table_props.get('show_header', True):
            pdf.set_font('Arial', 'B', 10)
            for col in columns:
                pdf.cell(col_width, row_height, col.get('header', ''), border=1)
            pdf.ln()
        
        # Render data rows
        pdf.set_font('Arial', '', 10)
        for row in table_data[:20]:  # Limit rows for basic implementation
            for col in columns:
                value = self._resolve_data_path(col.get('data_source', ''), {{'row': row}})
                pdf.cell(col_width, row_height, str(value or ''), border=1)
            pdf.ln()
    
    @staticmethod
    def _hex_to_rgb(hex_color: str) -> tuple:
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    @staticmethod
    def _resolve_data_path(path: str, data: Dict[str, Any]) -> Any:
        """Resolve dot-notation data path."""
        try:
            parts = path.split('.')
            value = data
            for part in parts:
                if isinstance(value, dict):
                    value = value.get(part)
                elif hasattr(value, part):
                    value = getattr(value, part)
                else:
                    return None
                if value is None:
                    return None
            return value
        except Exception:
            return None


# Convenience function for direct usage
def generate_pdf(data: Dict[str, Any], output_path: str = None) -> bytes:
    """
    Generate PDF with provided data.
    
    Args:
        data: Data context for template rendering
        output_path: Optional output file path
        
    Returns:
        PDF content as bytes
    """
    generator = {class_name}()
    return generator.generate_pdf(data, output_path)
'''
        
        return code
    
    def _format_template_config(self, template_data: Dict[str, Any]) -> str:
        """Format template configuration as Python dictionary."""
        import json
        return json.dumps(template_data, indent=8)
    
    def _generate_init_file(self, context: Dict[str, Any]) -> str:
        """Generate __init__.py file."""
        return f'''"""
{context['template'].get('name', 'PDF Generator')} Package

Generated by PDFlex - Universal Form-to-PDF Generator
"""

from .{context['module_name']} import {context['class_name']}, generate_pdf

__version__ = "1.0.0"
__all__ = ["{context['class_name']}", "generate_pdf"]
'''
    
    def _generate_requirements(self, context: Dict[str, Any]) -> str:
        """Generate requirements.txt file."""
        return '\n'.join(context['dependencies']) + '\n'
    
    def _generate_setup_file(self, context: Dict[str, Any]) -> str:
        """Generate setup.py file."""
        template_data = context['template']
        return f'''"""
Setup script for {template_data.get('name', 'PDF Generator')}
"""

from setuptools import setup, find_packages

setup(
    name="{context['package_name']}",
    version="1.0.0",
    description="{template_data.get('description', 'PDF Generator')}",
    author="Generated by PDFlex",
    packages=find_packages(),
    install_requires=[
{chr(10).join(f'        "{dep}",' for dep in context['dependencies'])}
    ],
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
'''
    
    def _generate_readme(self, context: Dict[str, Any]) -> str:
        """Generate README.md file."""
        template_data = context['template']
        return f'''# {template_data.get('name', 'PDF Generator')}

{template_data.get('description', 'PDF Generator created with PDFlex')}

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```python
from {context['module_name']} import generate_pdf

# Sample data
data = {{
    "title": "Sample Document",
    "content": "This is a sample PDF document.",
    "items": [
        {{"name": "Item 1", "value": 100}},
        {{"name": "Item 2", "value": 200}},
    ]
}}

# Generate PDF
pdf_content = generate_pdf(data)

# Save to file
with open("output.pdf", "wb") as f:
    f.write(pdf_content)
```

## Generated by PDFlex

This package was automatically generated by [PDFlex](https://github.com/your-org/pdflex) - Universal Form-to-PDF Generator.
'''
    
    def _generate_utils_module(self, context: Dict[str, Any]) -> str:
        """Generate utility functions module."""
        return '''"""
Utility functions for PDF generation.
"""

from typing import Dict, Any, Union
import base64
import io


def encode_image_to_base64(image_path: str) -> str:
    """
    Encode image file to base64 string.
    
    Args:
        image_path: Path to image file
        
    Returns:
        Base64 encoded image string
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def validate_data(data: Dict[str, Any], required_fields: list) -> list:
    """
    Validate that required fields are present in data.
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Returns:
        List of missing field names
    """
    missing = []
    for field in required_fields:
        if field not in data or data[field] is None:
            missing.append(field)
    return missing


def format_currency(value: Union[int, float], currency: str = "USD") -> str:
    """
    Format number as currency string.
    
    Args:
        value: Numeric value
        currency: Currency code
        
    Returns:
        Formatted currency string
    """
    try:
        return f"{currency} {float(value):,.2f}"
    except (ValueError, TypeError):
        return str(value)


def safe_get(data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """
    Safely get nested value from dictionary using dot notation.
    
    Args:
        data: Dictionary to search
        path: Dot-notation path (e.g., 'user.profile.name')
        default: Default value if path not found
        
    Returns:
        Value at path or default
    """
    try:
        parts = path.split('.')
        value = data
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            else:
                return default
            if value is None:
                return default
        return value
    except Exception:
        return default
'''
    
    def _generate_example(self, context: Dict[str, Any]) -> str:
        """Generate example usage script."""
        template_data = context['template']
        return f'''#!/usr/bin/env python3
"""
Example usage of {template_data.get('name', 'PDF Generator')}

This script demonstrates how to use the generated PDF generator
with sample data.
"""

from {context['module_name']} import generate_pdf


def main():
    """Main example function."""
    # Sample data - customize this based on your template
    sample_data = {{
        "title": "Sample Document",
        "date": "2024-01-01",
        "content": "This is a sample PDF document generated by PDFlex.",
        "items": [
            {{"name": "Item 1", "description": "First item", "quantity": 2, "price": 10.00}},
            {{"name": "Item 2", "description": "Second item", "quantity": 1, "price": 25.00}},
            {{"name": "Item 3", "description": "Third item", "quantity": 3, "price": 5.00}},
        ],
        "total": 55.00,
        "customer": {{
            "name": "John Doe",
            "email": "<EMAIL>",
            "address": "123 Main St, City, State 12345"
        }}
    }}
    
    try:
        # Generate PDF
        print("Generating PDF...")
        pdf_content = generate_pdf(sample_data)
        
        # Save to file
        output_file = "sample_output.pdf"
        with open(output_file, "wb") as f:
            f.write(pdf_content)
        
        print(f"PDF generated successfully: {{output_file}}")
        print(f"File size: {{len(pdf_content)}} bytes")
        
    except Exception as e:
        print(f"Error generating PDF: {{e}}")


if __name__ == "__main__":
    main()
'''
