"""
Template rendering system with Jinja2 integration.

This module provides advanced template rendering capabilities including:
- Data binding and variable substitution
- Conditional rendering logic
- Loop and iteration support
- Template compilation and caching
"""

import re
from typing import Dict, Any, List, Optional, Union
from jinja2 import Environment, BaseLoader, Template, TemplateError, meta
from jinja2.sandbox import SandboxedEnvironment
import json
import logging

logger = logging.getLogger(__name__)


class TemplateRenderer:
    """
    Advanced template renderer with Jinja2 integration.
    
    Provides safe template rendering with data binding, conditional logic,
    and loop support for PDF template generation.
    """
    
    def __init__(self, sandbox: bool = True):
        """
        Initialize template renderer.
        
        Args:
            sandbox: Whether to use sandboxed environment for security
        """
        self.sandbox = sandbox
        self.env = self._create_environment()
        self._template_cache = {}
    
    def _create_environment(self) -> Environment:
        """Create Jinja2 environment with custom configuration."""
        env_class = SandboxedEnvironment if self.sandbox else Environment
        
        env = env_class(
            loader=BaseLoader(),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
            undefined=self._create_undefined_handler(),
        )
        
        # Add custom filters
        env.filters.update({
            'format_currency': self._format_currency,
            'format_date': self._format_date,
            'format_number': self._format_number,
            'truncate_words': self._truncate_words,
            'default_if_empty': self._default_if_empty,
            'safe_get': self._safe_get,
        })
        
        # Add custom functions
        env.globals.update({
            'len': len,
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'sum': sum,
            'min': min,
            'max': max,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
        })
        
        return env
    
    def _create_undefined_handler(self):
        """Create custom undefined handler for missing variables."""
        from jinja2 import Undefined
        
        class SafeUndefined(Undefined):
            def __str__(self):
                return f"[UNDEFINED: {self._undefined_name}]"
            
            def __bool__(self):
                return False
        
        return SafeUndefined
    
    def render_template(self, template_content: str, context: Dict[str, Any]) -> str:
        """
        Render template with provided context.
        
        Args:
            template_content: Template content string
            context: Template context variables
            
        Returns:
            Rendered template content
            
        Raises:
            TemplateError: If template rendering fails
        """
        try:
            template = self.env.from_string(template_content)
            return template.render(**context)
        except TemplateError as e:
            logger.error(f"Template rendering failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during template rendering: {e}")
            raise TemplateError(f"Template rendering failed: {e}")
    
    def render_element_content(self, element: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Render dynamic content for a template element.
        
        Args:
            element: Element configuration dictionary
            context: Template context variables
            
        Returns:
            Element with rendered content
        """
        rendered_element = element.copy()
        
        # Render conditional visibility
        if element.get('condition'):
            try:
                condition_result = self.evaluate_condition(element['condition'], context)
                rendered_element['visible'] = bool(condition_result)
            except Exception as e:
                logger.warning(f"Failed to evaluate condition '{element['condition']}': {e}")
                rendered_element['visible'] = True
        
        # Render element-specific content
        element_type = element.get('element_type')
        
        if element_type == 'text':
            rendered_element = self._render_text_element(rendered_element, context)
        elif element_type == 'image':
            rendered_element = self._render_image_element(rendered_element, context)
        elif element_type == 'table':
            rendered_element = self._render_table_element(rendered_element, context)
        
        return rendered_element
    
    def _render_text_element(self, element: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Render text element content."""
        text_props = element.get('text_properties', {})
        content = text_props.get('content', '')
        
        if content:
            try:
                rendered_content = self.render_template(content, context)
                element['text_properties']['content'] = rendered_content
            except TemplateError as e:
                logger.error(f"Failed to render text content: {e}")
                element['text_properties']['content'] = f"[RENDER ERROR: {e}]"
        
        return element
    
    def _render_image_element(self, element: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Render image element content."""
        image_props = element.get('image_properties', {})
        
        # Render dynamic image source
        if image_props.get('image_data_source'):
            data_source = image_props['image_data_source']
            try:
                image_url = self._resolve_data_path(data_source, context)
                if image_url:
                    element['image_properties']['image_url'] = str(image_url)
            except Exception as e:
                logger.error(f"Failed to resolve image data source '{data_source}': {e}")
        
        # Render alt text
        if image_props.get('alt_text'):
            try:
                rendered_alt = self.render_template(image_props['alt_text'], context)
                element['image_properties']['alt_text'] = rendered_alt
            except TemplateError as e:
                logger.error(f"Failed to render image alt text: {e}")
        
        return element
    
    def _render_table_element(self, element: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Render table element content."""
        table_props = element.get('table_properties', {})
        data_source = table_props.get('data_source')
        
        if data_source:
            try:
                # Resolve table data
                table_data = self._resolve_data_path(data_source, context)
                if isinstance(table_data, list):
                    element['table_properties']['resolved_data'] = table_data
                else:
                    logger.warning(f"Table data source '{data_source}' did not resolve to a list")
                    element['table_properties']['resolved_data'] = []
            except Exception as e:
                logger.error(f"Failed to resolve table data source '{data_source}': {e}")
                element['table_properties']['resolved_data'] = []
        
        return element
    
    def evaluate_condition(self, condition: str, context: Dict[str, Any]) -> Any:
        """
        Evaluate a conditional expression.
        
        Args:
            condition: Jinja2 conditional expression
            context: Template context variables
            
        Returns:
            Evaluation result
        """
        template_str = f"{{{{ {condition} }}}}"
        result = self.render_template(template_str, context)
        
        # Convert string result to appropriate type
        if result.lower() in ('true', 'false'):
            return result.lower() == 'true'
        elif result.isdigit():
            return int(result)
        elif self._is_float(result):
            return float(result)
        else:
            return result
    
    def _resolve_data_path(self, path: str, context: Dict[str, Any]) -> Any:
        """
        Resolve a dot-notation data path in the context.
        
        Args:
            path: Dot-notation path (e.g., 'user.profile.name')
            context: Template context variables
            
        Returns:
            Resolved value or None if not found
        """
        try:
            parts = path.split('.')
            value = context
            
            for part in parts:
                if isinstance(value, dict):
                    value = value.get(part)
                elif hasattr(value, part):
                    value = getattr(value, part)
                else:
                    return None
                
                if value is None:
                    return None
            
            return value
        except Exception:
            return None
    
    def get_template_variables(self, template_content: str) -> List[str]:
        """
        Extract variable names from template content.
        
        Args:
            template_content: Template content string
            
        Returns:
            List of variable names used in the template
        """
        try:
            ast = self.env.parse(template_content)
            return list(meta.find_undeclared_variables(ast))
        except Exception as e:
            logger.error(f"Failed to extract template variables: {e}")
            return []
    
    # Custom filter implementations
    @staticmethod
    def _format_currency(value: Union[int, float], currency: str = 'USD') -> str:
        """Format number as currency."""
        try:
            return f"{currency} {float(value):,.2f}"
        except (ValueError, TypeError):
            return str(value)
    
    @staticmethod
    def _format_date(value: str, format_str: str = '%Y-%m-%d') -> str:
        """Format date string."""
        try:
            from datetime import datetime
            if isinstance(value, str):
                # Try to parse common date formats
                for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        dt = datetime.strptime(value, fmt)
                        return dt.strftime(format_str)
                    except ValueError:
                        continue
            return str(value)
        except Exception:
            return str(value)
    
    @staticmethod
    def _format_number(value: Union[int, float], decimals: int = 2) -> str:
        """Format number with specified decimal places."""
        try:
            return f"{float(value):,.{decimals}f}"
        except (ValueError, TypeError):
            return str(value)
    
    @staticmethod
    def _truncate_words(text: str, length: int = 50) -> str:
        """Truncate text to specified word count."""
        words = str(text).split()
        if len(words) <= length:
            return text
        return ' '.join(words[:length]) + '...'
    
    @staticmethod
    def _default_if_empty(value: Any, default: Any = '') -> Any:
        """Return default value if input is empty."""
        if not value or (isinstance(value, str) and not value.strip()):
            return default
        return value
    
    @staticmethod
    def _safe_get(obj: Dict[str, Any], key: str, default: Any = None) -> Any:
        """Safely get value from dictionary."""
        if isinstance(obj, dict):
            return obj.get(key, default)
        return default
    
    @staticmethod
    def _is_float(value: str) -> bool:
        """Check if string represents a float."""
        try:
            float(value)
            return True
        except ValueError:
            return False
