"""
PDF Generation Engines Package

This package contains the multi-language code generation engines for PDFlex.
Each engine is responsible for generating production-ready code in a specific
programming language based on PDF templates.
"""

from .base import PDFEngine, EngineError, ValidationError
from .python_engine import PythonEngine
from .javascript_engine import JavaScriptEngine
from .php_engine import PHPEngine

__all__ = [
    'PDFEngine',
    'EngineError', 
    'ValidationError',
    'PythonEngine',
    'JavaScriptEngine',
    'PHPEngine',
]

# Registry of available engines
ENGINE_REGISTRY = {
    'python': PythonEngine,
    'javascript': JavaScriptEngine,
    'php': PHPEngine,
}

def get_engine(language: str) -> PDFEngine:
    """
    Get an engine instance for the specified language.
    
    Args:
        language: The target programming language
        
    Returns:
        PDFEngine instance
        
    Raises:
        ValueError: If the language is not supported
    """
    if language not in ENGINE_REGISTRY:
        available = ', '.join(ENGINE_REGISTRY.keys())
        raise ValueError(f"Unsupported language '{language}'. Available: {available}")
    
    return ENGINE_REGISTRY[language]()

def get_supported_languages():
    """Get list of supported programming languages."""
    return list(ENGINE_REGISTRY.keys())
