"""
JavaScript PDF generation engine.

Generates Node.js and browser-compatible JavaScript code using
jsPDF library with proper NPM package structure.
"""

from typing import Dict, List, Any
from .base import PDFEngine, EngineError


class JavaScriptEngine(PDFEngine):
    """
    JavaScript code generation engine for PDF templates.
    
    Generates JavaScript packages with:
    - jsPDF for PDF generation
    - NPM package structure with package.json
    - Both CommonJS and ES6 module formats
    - Browser-compatible bundle generation
    """
    
    @property
    def language(self) -> str:
        return 'javascript'
    
    @property
    def file_extension(self) -> str:
        return '.js'
    
    @property
    def package_manager(self) -> str:
        return 'npm'
    
    def get_dependencies(self, template_data: Dict[str, Any]) -> List[str]:
        """Get JavaScript dependencies based on template features."""
        dependencies = [
            'jspdf@^2.5.0',
        ]
        
        # Check for additional features
        elements = template_data.get('elements', [])
        
        for element in elements:
            element_type = element.get('element_type')
            
            # Add canvas support for images
            if element_type == 'image':
                dependencies.append('canvas@^2.11.0')
            
            # Add table plugin for complex tables
            elif element_type == 'table':
                dependencies.append('jspdf-autotable@^3.5.0')
        
        # Add template engine if needed
        if any('{{' in str(elem.get('text_properties', {}).get('content', '')) 
               for elem in elements if elem.get('element_type') == 'text'):
            dependencies.append('mustache@^4.2.0')
        
        return dependencies
    
    def generate_code(self, template_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate JavaScript code files for the template."""
        options = options or {}
        context = self.get_template_context(template_data)
        
        files = {}
        
        # Generate main module (CommonJS)
        files[f"{context['module_name']}.js"] = self._generate_main_module(context, 'commonjs')
        
        # Generate ES6 module
        files[f"{context['module_name']}.mjs"] = self._generate_main_module(context, 'es6')
        
        # Generate package files
        files['package.json'] = self._generate_package_json(context)
        files['README.md'] = self._generate_readme(context)
        
        # Generate TypeScript definitions
        files[f"{context['module_name']}.d.ts"] = self._generate_typescript_definitions(context)
        
        # Generate browser bundle
        files['browser.js'] = self._generate_browser_bundle(context)
        
        # Generate example files
        files['example.js'] = self._generate_example(context, 'node')
        files['example.html'] = self._generate_browser_example(context)
        
        # Apply optimizations
        optimization_level = options.get('optimization_level', 1)
        if optimization_level > 1:
            for file_path, content in files.items():
                if file_path.endswith('.js') or file_path.endswith('.mjs'):
                    files[file_path] = self.optimize_code(content, optimization_level)
        
        return files
    
    def _generate_main_module(self, context: Dict[str, Any], module_format: str = 'commonjs') -> str:
        """Generate the main PDF generator module."""
        template_data = context['template']
        class_name = context['class_name']
        
        # Determine imports based on module format
        if module_format == 'es6':
            imports = "import { jsPDF } from 'jspdf';"
            if 'jspdf-autotable' in context['dependencies']:
                imports += "\nimport 'jspdf-autotable';"
            if 'mustache' in context['dependencies']:
                imports += "\nimport Mustache from 'mustache';"
            exports = f"export {{ {class_name}, generatePDF }};"
        else:
            imports = "const { jsPDF } = require('jspdf');"
            if 'jspdf-autotable' in context['dependencies']:
                imports += "\nrequire('jspdf-autotable');"
            if 'mustache' in context['dependencies']:
                imports += "\nconst Mustache = require('mustache');"
            exports = f"module.exports = {{ {class_name}, generatePDF }};"
        
        code = f'''/**
 * {template_data.get('description', f'PDF Generator for {template_data.get("name", "Template")}')}
 * 
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */

{imports}

/**
 * PDF Generator class for {template_data.get('name', 'Template')}.
 * 
 * This class generates PDF documents based on the template configuration
 * and provided data context.
 */
class {class_name} {{
    constructor() {{
        this.templateConfig = {self._format_template_config(template_data)};
        const dimensions = this.getPageDimensions();
        this.pageWidth = dimensions.width;
        this.pageHeight = dimensions.height;
    }}
    
    /**
     * Get page dimensions based on template configuration.
     * @returns {{Object}} Page dimensions with width and height
     */
    getPageDimensions() {{
        const config = this.templateConfig;
        const pageSize = config.page_size || 'A4';
        const orientation = config.orientation || 'portrait';
        
        // Standard page sizes in mm
        const sizes = {{
            'A4': {{ width: 210, height: 297 }},
            'A3': {{ width: 297, height: 420 }},
            'A5': {{ width: 148, height: 210 }},
            'LETTER': {{ width: 215.9, height: 279.4 }},
            'LEGAL': {{ width: 215.9, height: 355.6 }}
        }};
        
        let dimensions;
        if (pageSize === 'CUSTOM') {{
            dimensions = {{
                width: config.custom_width || 210,
                height: config.custom_height || 297
            }};
        }} else {{
            dimensions = sizes[pageSize] || sizes['A4'];
        }}
        
        if (orientation === 'landscape') {{
            return {{ width: dimensions.height, height: dimensions.width }};
        }}
        
        return dimensions;
    }}
    
    /**
     * Generate PDF document with provided data.
     * @param {{Object}} data - Data context for template rendering
     * @param {{string}} [outputType='blob'] - Output type: 'blob', 'arraybuffer', 'datauristring'
     * @returns {{*}} PDF content in specified format
     */
    generatePDF(data, outputType = 'blob') {{
        const pdf = new jsPDF({{
            orientation: this.pageWidth < this.pageHeight ? 'portrait' : 'landscape',
            unit: 'mm',
            format: [this.pageWidth, this.pageHeight]
        }});
        
        // Process template elements
        const elements = this.templateConfig.elements || [];
        elements.forEach(element => {{
            this.renderElement(pdf, element, data);
        }});
        
        // Return PDF in requested format
        switch (outputType) {{
            case 'arraybuffer':
                return pdf.output('arraybuffer');
            case 'datauristring':
                return pdf.output('datauristring');
            case 'blob':
            default:
                return pdf.output('blob');
        }}
    }}
    
    /**
     * Render a single template element.
     * @param {{jsPDF}} pdf - jsPDF instance
     * @param {{Object}} element - Element configuration
     * @param {{Object}} data - Data context
     */
    renderElement(pdf, element, data) {{
        if (!element.visible && element.visible !== undefined) {{
            return;
        }}
        
        const elementType = element.element_type;
        
        switch (elementType) {{
            case 'text':
                this.renderTextElement(pdf, element, data);
                break;
            case 'image':
                this.renderImageElement(pdf, element, data);
                break;
            case 'table':
                this.renderTableElement(pdf, element, data);
                break;
        }}
    }}
    
    /**
     * Render text element.
     * @param {{jsPDF}} pdf - jsPDF instance
     * @param {{Object}} element - Element configuration
     * @param {{Object}} data - Data context
     */
    renderTextElement(pdf, element, data) {{
        const textProps = element.text_properties || {{}};
        let content = textProps.content || '';
        
        // Render template content
        if (content.includes('{{{{') && content.includes('}}}}') && typeof Mustache !== 'undefined') {{
            content = Mustache.render(content, data);
        }}
        
        // Set font
        const fontFamily = textProps.font_family || 'helvetica';
        const fontSize = textProps.font_size || 12;
        const fontWeight = textProps.font_weight || 'normal';
        
        pdf.setFont(fontFamily, fontWeight === 'bold' ? 'bold' : 'normal');
        pdf.setFontSize(fontSize);
        
        // Set text color
        const textColor = textProps.text_color || '#000000';
        const rgb = this.hexToRgb(textColor);
        pdf.setTextColor(rgb.r, rgb.g, rgb.b);
        
        // Set position and render text
        const x = element.x || 0;
        const y = element.y || 0;
        const width = element.width || 0;
        const align = textProps.text_align || 'left';
        
        if (width > 0) {{
            pdf.text(content, x, y, {{ maxWidth: width, align: align }});
        }} else {{
            pdf.text(content, x, y);
        }}
    }}
    
    /**
     * Render image element.
     * @param {{jsPDF}} pdf - jsPDF instance
     * @param {{Object}} element - Element configuration
     * @param {{Object}} data - Data context
     */
    renderImageElement(pdf, element, data) {{
        const imageProps = element.image_properties || {{}};
        
        // Get image source
        let imageUrl = imageProps.image_url;
        if (!imageUrl && imageProps.image_data_source) {{
            imageUrl = this.resolveDataPath(imageProps.image_data_source, data);
        }}
        
        if (imageUrl) {{
            const x = element.x || 0;
            const y = element.y || 0;
            const width = element.width || 50;
            const height = element.height || 50;
            
            try {{
                pdf.addImage(imageUrl, 'JPEG', x, y, width, height);
            }} catch (error) {{
                // Fallback: render placeholder text
                pdf.setFontSize(10);
                pdf.text(`[Image Error: ${{error.message}}]`, x, y);
            }}
        }}
    }}
    
    /**
     * Render table element.
     * @param {{jsPDF}} pdf - jsPDF instance
     * @param {{Object}} element - Element configuration
     * @param {{Object}} data - Data context
     */
    renderTableElement(pdf, element, data) {{
        const tableProps = element.table_properties || {{}};
        const columns = tableProps.columns || [];
        const dataSource = tableProps.data_source;
        
        if (!columns.length || !dataSource) {{
            return;
        }}
        
        // Get table data
        const tableData = this.resolveDataPath(dataSource, data);
        if (!Array.isArray(tableData)) {{
            return;
        }}
        
        // Use autoTable plugin if available
        if (typeof pdf.autoTable === 'function') {{
            const headers = columns.map(col => col.header || col.name);
            const rows = tableData.map(row => 
                columns.map(col => {{
                    const value = this.resolveDataPath(col.data_source || col.name, {{ row }});
                    return String(value || '');
                }})
            );
            
            pdf.autoTable({{
                head: [headers],
                body: rows,
                startY: element.y || 20,
                margin: {{ left: element.x || 10 }},
                styles: {{
                    fontSize: 10,
                    cellPadding: tableProps.cell_padding || 3
                }},
                headStyles: {{
                    fillColor: this.hexToRgb(tableProps.header_background_color || '#f0f0f0')
                }}
            }});
        }} else {{
            // Basic table rendering fallback
            const x = element.x || 0;
            let y = element.y || 0;
            const cellHeight = 8;
            const cellWidth = (element.width || 100) / columns.length;
            
            // Render headers
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'bold');
            columns.forEach((col, i) => {{
                pdf.rect(x + i * cellWidth, y, cellWidth, cellHeight);
                pdf.text(col.header || col.name, x + i * cellWidth + 2, y + 6);
            }});
            y += cellHeight;
            
            // Render data rows
            pdf.setFont('helvetica', 'normal');
            tableData.slice(0, 20).forEach(row => {{
                columns.forEach((col, i) => {{
                    const value = this.resolveDataPath(col.data_source || col.name, {{ row }});
                    pdf.rect(x + i * cellWidth, y, cellWidth, cellHeight);
                    pdf.text(String(value || ''), x + i * cellWidth + 2, y + 6);
                }});
                y += cellHeight;
            }});
        }}
    }}
    
    /**
     * Convert hex color to RGB object.
     * @param {{string}} hex - Hex color string
     * @returns {{Object}} RGB color object
     */
    hexToRgb(hex) {{
        const result = /^#?([a-f\\d]{{2}})([a-f\\d]{{2}})([a-f\\d]{{2}})$/i.exec(hex);
        return result ? {{
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        }} : {{ r: 0, g: 0, b: 0 }};
    }}
    
    /**
     * Resolve dot-notation data path.
     * @param {{string}} path - Dot-notation path
     * @param {{Object}} data - Data object
     * @returns {{*}} Resolved value or null
     */
    resolveDataPath(path, data) {{
        try {{
            const parts = path.split('.');
            let value = data;
            for (const part of parts) {{
                if (value && typeof value === 'object' && part in value) {{
                    value = value[part];
                }} else {{
                    return null;
                }}
            }}
            return value;
        }} catch (error) {{
            return null;
        }}
    }}
}}

/**
 * Convenience function for direct PDF generation.
 * @param {{Object}} data - Data context for template rendering
 * @param {{string}} [outputType='blob'] - Output type
 * @returns {{*}} PDF content in specified format
 */
function generatePDF(data, outputType = 'blob') {{
    const generator = new {class_name}();
    return generator.generatePDF(data, outputType);
}}

{exports}
'''
        
        return code
    
    def _format_template_config(self, template_data: Dict[str, Any]) -> str:
        """Format template configuration as JavaScript object."""
        import json
        return json.dumps(template_data, indent=8)
    
    def _generate_package_json(self, context: Dict[str, Any]) -> str:
        """Generate package.json file."""
        template_data = context['template']
        
        # Parse dependencies
        deps = {}
        for dep in context['dependencies']:
            if '@' in dep:
                name, version = dep.split('@', 1)
                deps[name] = f"^{version}"
            else:
                deps[dep] = "latest"
        
        package_json = {
            "name": context['package_name'],
            "version": "1.0.0",
            "description": template_data.get('description', 'PDF Generator'),
            "main": f"{context['module_name']}.js",
            "module": f"{context['module_name']}.mjs",
            "types": f"{context['module_name']}.d.ts",
            "browser": "browser.js",
            "scripts": {
                "test": "echo \"Error: no test specified\" && exit 1",
                "example": f"node example.js"
            },
            "keywords": ["pdf", "generator", "pdflex", "document"],
            "author": "Generated by PDFlex",
            "license": "MIT",
            "dependencies": deps,
            "engines": {
                "node": ">=12.0.0"
            }
        }
        
        import json
        return json.dumps(package_json, indent=2)
    
    def _generate_typescript_definitions(self, context: Dict[str, Any]) -> str:
        """Generate TypeScript definition file."""
        class_name = context['class_name']
        
        return f'''/**
 * TypeScript definitions for {context['template'].get('name', 'PDF Generator')}
 * Generated by PDFlex - Universal Form-to-PDF Generator
 */

export interface PageDimensions {{
    width: number;
    height: number;
}}

export interface GenerateOptions {{
    outputType?: 'blob' | 'arraybuffer' | 'datauristring';
}}

export declare class {class_name} {{
    constructor();
    
    getPageDimensions(): PageDimensions;
    generatePDF(data: Record<string, any>, outputType?: string): Blob | ArrayBuffer | string;
    renderElement(pdf: any, element: Record<string, any>, data: Record<string, any>): void;
    renderTextElement(pdf: any, element: Record<string, any>, data: Record<string, any>): void;
    renderImageElement(pdf: any, element: Record<string, any>, data: Record<string, any>): void;
    renderTableElement(pdf: any, element: Record<string, any>, data: Record<string, any>): void;
    hexToRgb(hex: string): {{ r: number; g: number; b: number }};
    resolveDataPath(path: string, data: Record<string, any>): any;
}}

export declare function generatePDF(data: Record<string, any>, outputType?: string): Blob | ArrayBuffer | string;
'''

    def _generate_browser_bundle(self, context: Dict[str, Any]) -> str:
        """Generate browser-compatible bundle."""
        class_name = context['class_name']

        return f'''/**
 * Browser bundle for {context['template'].get('name', 'PDF Generator')}
 * Generated by PDFlex - Universal Form-to-PDF Generator
 *
 * This file can be included directly in HTML pages.
 * Requires jsPDF to be loaded separately.
 */

(function(global) {{
    'use strict';

    // Check if jsPDF is available
    if (typeof jsPDF === 'undefined') {{
        throw new Error('jsPDF is required. Please include jsPDF before this script.');
    }}

    {self._generate_main_module(context, 'browser')}

    // Expose to global scope
    global.{class_name} = {class_name};
    global.generatePDF = generatePDF;

}})(typeof window !== 'undefined' ? window : this);
'''

    def _generate_readme(self, context: Dict[str, Any]) -> str:
        """Generate README.md file."""
        template_data = context['template']

        return f'''# {template_data.get('name', 'PDF Generator')}

{template_data.get('description', 'PDF Generator created with PDFlex')}

## Installation

```bash
npm install
```

## Usage

### Node.js (CommonJS)

```javascript
const {{ {context['class_name']}, generatePDF }} = require('./{context['module_name']}');

// Sample data
const data = {{
    title: "Sample Document",
    content: "This is a sample PDF document.",
    items: [
        {{ name: "Item 1", value: 100 }},
        {{ name: "Item 2", value: 200 }},
    ]
}};

// Generate PDF
const pdfBlob = generatePDF(data);

// In Node.js, convert blob to buffer and save
const fs = require('fs');
const buffer = Buffer.from(await pdfBlob.arrayBuffer());
fs.writeFileSync('output.pdf', buffer);
```

### ES6 Modules

```javascript
import {{ {context['class_name']}, generatePDF }} from './{context['module_name']}.mjs';

const data = {{ /* your data */ }};
const pdfBlob = generatePDF(data);

// Save or download the PDF
const url = URL.createObjectURL(pdfBlob);
const a = document.createElement('a');
a.href = url;
a.download = 'document.pdf';
a.click();
```

### Browser

```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="browser.js"></script>
</head>
<body>
    <script>
        const data = {{ /* your data */ }};
        const pdfBlob = generatePDF(data);

        // Download PDF
        const url = URL.createObjectURL(pdfBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'document.pdf';
        a.click();
    </script>
</body>
</html>
```

## API Reference

### Class: {context['class_name']}

#### Methods

- `constructor()` - Create new PDF generator instance
- `generatePDF(data, outputType)` - Generate PDF with provided data
  - `data` (Object) - Data context for template rendering
  - `outputType` (String) - Output format: 'blob', 'arraybuffer', 'datauristring'
  - Returns: PDF content in specified format

### Function: generatePDF(data, outputType)

Convenience function for direct PDF generation.

## Generated by PDFlex

This package was automatically generated by [PDFlex](https://github.com/your-org/pdflex) - Universal Form-to-PDF Generator.
'''

    def _generate_example(self, context: Dict[str, Any], environment: str = 'node') -> str:
        """Generate example usage script."""
        template_data = context['template']

        if environment == 'node':
            return f'''#!/usr/bin/env node
/**
 * Example usage of {template_data.get('name', 'PDF Generator')}
 *
 * This script demonstrates how to use the generated PDF generator
 * with sample data in Node.js environment.
 */

const {{ {context['class_name']}, generatePDF }} = require('./{context['module_name']}');
const fs = require('fs');

async function main() {{
    // Sample data - customize this based on your template
    const sampleData = {{
        title: "Sample Document",
        date: "2024-01-01",
        content: "This is a sample PDF document generated by PDFlex.",
        items: [
            {{ name: "Item 1", description: "First item", quantity: 2, price: 10.00 }},
            {{ name: "Item 2", description: "Second item", quantity: 1, price: 25.00 }},
            {{ name: "Item 3", description: "Third item", quantity: 3, price: 5.00 }},
        ],
        total: 55.00,
        customer: {{
            name: "John Doe",
            email: "<EMAIL>",
            address: "123 Main St, City, State 12345"
        }}
    }};

    try {{
        // Generate PDF
        console.log("Generating PDF...");
        const pdfBlob = generatePDF(sampleData);

        // Convert blob to buffer and save
        const arrayBuffer = await pdfBlob.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        const outputFile = "sample_output.pdf";
        fs.writeFileSync(outputFile, buffer);

        console.log(`PDF generated successfully: ${{outputFile}}`);
        console.log(`File size: ${{buffer.length}} bytes`);

    }} catch (error) {{
        console.error(`Error generating PDF: ${{error.message}}`);
    }}
}}

if (require.main === module) {{
    main();
}}
'''

    def _generate_browser_example(self, context: Dict[str, Any]) -> str:
        """Generate browser example HTML file."""
        template_data = context['template']

        return f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{template_data.get('name', 'PDF Generator')} - Browser Example</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }}
        .container {{
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        button {{
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }}
        button:hover {{
            background: #005a87;
        }}
        button:disabled {{
            background: #ccc;
            cursor: not-allowed;
        }}
        .status {{
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }}
        .status.success {{
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }}
        .status.error {{
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }}
        pre {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }}
    </style>
</head>
<body>
    <h1>{template_data.get('name', 'PDF Generator')} - Browser Example</h1>

    <p>This example demonstrates how to use the generated PDF generator in a web browser.</p>

    <div class="container">
        <h2>Sample Data</h2>
        <pre id="sampleData"></pre>

        <button id="generateBtn" onclick="generatePDF()">Generate PDF</button>
        <div id="status"></div>
    </div>

    <div class="container">
        <h2>Instructions</h2>
        <ol>
            <li>Click "Generate PDF" to create a PDF with the sample data</li>
            <li>The PDF will be automatically downloaded</li>
            <li>Modify the sample data in the code to test different scenarios</li>
        </ol>
    </div>

    <!-- Include jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Include jsPDF AutoTable plugin if needed -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>

    <!-- Include the generated PDF generator -->
    <script src="browser.js"></script>

    <script>
        // Sample data - customize this based on your template
        const sampleData = {{
            title: "Sample Document",
            date: new Date().toISOString().split('T')[0],
            content: "This is a sample PDF document generated by PDFlex in the browser.",
            items: [
                {{ name: "Item 1", description: "First item", quantity: 2, price: 10.00 }},
                {{ name: "Item 2", description: "Second item", quantity: 1, price: 25.00 }},
                {{ name: "Item 3", description: "Third item", quantity: 3, price: 5.00 }},
            ],
            total: 55.00,
            customer: {{
                name: "John Doe",
                email: "<EMAIL>",
                address: "123 Main St, City, State 12345"
            }}
        }};

        // Display sample data
        document.getElementById('sampleData').textContent = JSON.stringify(sampleData, null, 2);

        function showStatus(message, type = 'success') {{
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${{type}}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }}

        function generatePDF() {{
            const button = document.getElementById('generateBtn');
            button.disabled = true;
            button.textContent = 'Generating...';

            try {{
                // Generate PDF using the global function
                const pdfBlob = window.generatePDF(sampleData);

                // Create download link
                const url = URL.createObjectURL(pdfBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'sample_document.pdf';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showStatus('PDF generated and downloaded successfully!', 'success');

            }} catch (error) {{
                console.error('Error generating PDF:', error);
                showStatus(`Error generating PDF: ${{error.message}}`, 'error');
            }} finally {{
                button.disabled = false;
                button.textContent = 'Generate PDF';
            }}
        }}

        // Hide status initially
        document.getElementById('status').style.display = 'none';
    </script>
</body>
</html>
'''
