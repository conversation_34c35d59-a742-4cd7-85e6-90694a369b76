"""
Django admin configuration for PDF generation models.

This module provides comprehensive admin interfaces for managing
PDF templates, elements, generated code, and related data.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count
from django.utils import timezone

from .models import (
    PDFTemplate, PDFElement, TextElement, ImageElement,
    TableElement, ContainerElement, TemplatePreview,
    TemplateVersion, GeneratedCode
)


class PDFElementInline(admin.TabularInline):
    """Inline admin for PDF elements."""
    model = PDFElement
    extra = 0
    fields = ('name', 'element_type', 'x', 'y', 'width', 'height', 'visible', 'order')
    readonly_fields = ('id',)
    ordering = ('order', 'created_at')


class TemplateVersionInline(admin.TabularInline):
    """Inline admin for template versions."""
    model = TemplateVersion
    extra = 0
    fields = ('version_number', 'change_type', 'change_description', 'created_at', 'is_current')
    readonly_fields = ('created_at',)
    ordering = ('-created_at',)


@admin.register(PDFTemplate)
class PDFTemplateAdmin(admin.ModelAdmin):
    """Admin interface for PDF templates."""

    list_display = (
        'name', 'status', 'page_size', 'orientation', 'element_count',
        'usage_count', 'created_by', 'created_at', 'updated_at'
    )
    list_filter = (
        'status', 'page_size', 'orientation', 'created_at', 'updated_at'
    )
    search_fields = ('name', 'description', 'slug', 'created_by__username')
    readonly_fields = (
        'id', 'slug', 'created_at', 'updated_at', 'usage_count', 'last_used',
        'element_count', 'preview_link', 'generate_code_links'
    )

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'status', 'version')
        }),
        ('Page Configuration', {
            'fields': (
                'page_size', 'orientation', 'custom_width', 'custom_height',
                'margin_top', 'margin_bottom', 'margin_left', 'margin_right'
            )
        }),
        ('Template Inheritance', {
            'fields': ('parent_template',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('metadata', 'settings'),
            'classes': ('collapse',)
        }),
        ('Ownership & Timestamps', {
            'fields': ('created_by', 'organization', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('Usage Statistics', {
            'fields': ('usage_count', 'last_used'),
            'classes': ('collapse',)
        }),
        ('Actions', {
            'fields': ('preview_link', 'generate_code_links'),
            'classes': ('collapse',)
        })
    )

    inlines = [PDFElementInline, TemplateVersionInline]

    def element_count(self, obj):
        """Return the number of elements in the template."""
        return obj.elements.count()
    element_count.short_description = 'Elements'

    def preview_link(self, obj):
        """Return a link to preview the template."""
        if obj.pk:
            url = reverse('pdf_generation:template_preview', args=[obj.pk])
            return format_html('<a href="{}" target="_blank">Preview Template</a>', url)
        return '-'
    preview_link.short_description = 'Preview'

    def generate_code_links(self, obj):
        """Return links to generate code in different languages."""
        if obj.pk:
            links = []
            languages = ['python', 'javascript', 'php']
            for lang in languages:
                links.append(f'<a href="#" onclick="generateCode(\'{obj.pk}\', \'{lang}\')">{lang.title()}</a>')
            return mark_safe(' | '.join(links))
        return '-'
    generate_code_links.short_description = 'Generate Code'

    def get_queryset(self, request):
        """Optimize queryset with select_related and prefetch_related."""
        return super().get_queryset(request).select_related('created_by', 'parent_template').annotate(
            element_count=Count('elements')
        )

    def save_model(self, request, obj, form, change):
        """Set created_by to current user if not set."""
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PDFElement)
class PDFElementAdmin(admin.ModelAdmin):
    """Admin interface for PDF elements."""

    list_display = (
        'name', 'template', 'element_type', 'position', 'size',
        'visible', 'order', 'created_at'
    )
    list_filter = ('element_type', 'visible', 'template__status', 'created_at')
    search_fields = ('name', 'template__name', 'data_source')
    readonly_fields = ('id', 'created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('template', 'name', 'element_type', 'parent_element')
        }),
        ('Position & Size', {
            'fields': ('position_type', 'x', 'y', 'width', 'height', 'order')
        }),
        ('Styling & Behavior', {
            'fields': ('style', 'condition', 'data_source', 'visible', 'locked')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def position(self, obj):
        """Return formatted position."""
        return f"({obj.x}, {obj.y})"
    position.short_description = 'Position (x, y)'

    def size(self, obj):
        """Return formatted size."""
        width = obj.width or 'auto'
        height = obj.height or 'auto'
        return f"{width} × {height}"
    size.short_description = 'Size (W × H)'

    def get_queryset(self, request):
        """Optimize queryset."""
        return super().get_queryset(request).select_related('template', 'parent_element')


@admin.register(TextElement)
class TextElementAdmin(admin.ModelAdmin):
    """Admin interface for text elements."""

    list_display = ('element', 'content_preview', 'font_family', 'font_size', 'text_color')
    list_filter = ('font_family', 'font_weight', 'text_align')
    search_fields = ('element__name', 'content')
    readonly_fields = ('element',)

    def content_preview(self, obj):
        """Return truncated content preview."""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content'


@admin.register(ImageElement)
class ImageElementAdmin(admin.ModelAdmin):
    """Admin interface for image elements."""

    list_display = ('element', 'image_source', 'fit_mode', 'alignment', 'quality')
    list_filter = ('fit_mode', 'alignment')
    search_fields = ('element__name', 'image_url', 'alt_text')
    readonly_fields = ('element',)

    def image_source(self, obj):
        """Return image source type."""
        if obj.image_file:
            return format_html('<a href="{}" target="_blank">File</a>', obj.image_file.url)
        elif obj.image_url:
            return format_html('<a href="{}" target="_blank">URL</a>', obj.image_url)
        elif obj.image_data_source:
            return f"Dynamic: {obj.image_data_source}"
        return "No source"
    image_source.short_description = 'Source'


@admin.register(TableElement)
class TableElementAdmin(admin.ModelAdmin):
    """Admin interface for table elements."""

    list_display = ('element', 'column_count', 'data_source', 'show_header', 'stripe_rows')
    list_filter = ('show_header', 'stripe_rows', 'border_style')
    search_fields = ('element__name', 'data_source')
    readonly_fields = ('element',)

    def column_count(self, obj):
        """Return number of columns."""
        return len(obj.columns) if obj.columns else 0
    column_count.short_description = 'Columns'


@admin.register(ContainerElement)
class ContainerElementAdmin(admin.ModelAdmin):
    """Admin interface for container elements."""

    list_display = ('element', 'flex_direction', 'justify_content', 'align_items', 'gap')
    list_filter = ('flex_direction', 'justify_content', 'align_items')
    search_fields = ('element__name',)
    readonly_fields = ('element',)


@admin.register(TemplatePreview)
class TemplatePreviewAdmin(admin.ModelAdmin):
    """Admin interface for template previews."""

    list_display = (
        'template', 'status', 'generated_by', 'generation_time',
        'created_at', 'preview_files'
    )
    list_filter = ('status', 'created_at', 'generated_by')
    search_fields = ('template__name', 'generated_by__username')
    readonly_fields = (
        'id', 'template', 'generated_by', 'generation_time',
        'created_at', 'updated_at', 'preview_files'
    )

    fieldsets = (
        ('Preview Information', {
            'fields': ('template', 'status', 'generated_by', 'generation_time')
        }),
        ('Sample Data', {
            'fields': ('sample_data',),
            'classes': ('collapse',)
        }),
        ('Generated Files', {
            'fields': ('preview_image', 'preview_pdf', 'preview_files')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def preview_files(self, obj):
        """Return links to preview files."""
        links = []
        if obj.preview_pdf:
            links.append(format_html('<a href="{}" target="_blank">PDF</a>', obj.preview_pdf.url))
        if obj.preview_image:
            links.append(format_html('<a href="{}" target="_blank">Image</a>', obj.preview_image.url))
        return mark_safe(' | '.join(links)) if links else 'No files'
    preview_files.short_description = 'Files'

    def get_queryset(self, request):
        """Optimize queryset."""
        return super().get_queryset(request).select_related('template', 'generated_by')


@admin.register(TemplateVersion)
class TemplateVersionAdmin(admin.ModelAdmin):
    """Admin interface for template versions."""

    list_display = (
        'template', 'version_number', 'change_type', 'is_current',
        'created_by', 'created_at'
    )
    list_filter = ('change_type', 'is_current', 'created_at')
    search_fields = ('template__name', 'version_number', 'change_description')
    readonly_fields = (
        'id', 'template', 'version_number', 'created_by', 'created_at',
        'template_data', 'elements_data'
    )

    fieldsets = (
        ('Version Information', {
            'fields': ('template', 'version_number', 'change_type', 'is_current')
        }),
        ('Change Details', {
            'fields': ('change_description', 'created_by', 'created_at')
        }),
        ('Rollback Information', {
            'fields': ('rollback_from',),
            'classes': ('collapse',)
        }),
        ('Snapshot Data', {
            'fields': ('template_data', 'elements_data'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        """Optimize queryset."""
        return super().get_queryset(request).select_related('template', 'created_by', 'rollback_from')


@admin.register(GeneratedCode)
class GeneratedCodeAdmin(admin.ModelAdmin):
    """Admin interface for generated code."""

    list_display = (
        'template', 'language', 'status', 'generated_by',
        'generation_time', 'download_count', 'created_at', 'download_link'
    )
    list_filter = ('language', 'status', 'created_at', 'expires_at')
    search_fields = ('template__name', 'generated_by__username')
    readonly_fields = (
        'id', 'template', 'generated_by', 'generation_time',
        'download_count', 'created_at', 'updated_at', 'download_link'
    )

    fieldsets = (
        ('Generation Information', {
            'fields': ('template', 'language', 'status', 'generated_by', 'generation_time')
        }),
        ('Framework Options', {
            'fields': ('framework_options',),
            'classes': ('collapse',)
        }),
        ('Generated Package', {
            'fields': ('code_package', 'download_link', 'download_count')
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timestamps & Expiry', {
            'fields': ('created_at', 'updated_at', 'expires_at'),
            'classes': ('collapse',)
        })
    )

    def download_link(self, obj):
        """Return download link for completed packages."""
        if obj.status == GeneratedCode.Status.COMPLETED and obj.code_package:
            url = reverse('pdf_generation:download_code', args=[obj.id])
            return format_html('<a href="{}" target="_blank">Download Package</a>', url)
        return 'Not available'
    download_link.short_description = 'Download'

    def get_queryset(self, request):
        """Optimize queryset."""
        return super().get_queryset(request).select_related('template', 'generated_by')


# Custom admin site configuration
admin.site.site_header = 'PDFlex Administration'
admin.site.site_title = 'PDFlex Admin'
admin.site.index_title = 'Welcome to PDFlex Administration'

# Add custom CSS for better admin interface
class PDFGenerationAdminConfig:
    """Custom admin configuration for PDF generation app."""

    class Media:
        css = {
            'all': ('pdf_generation/admin.css',)
        }
        js = ('pdf_generation/admin.js',)
