from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from django.core.exceptions import ValidationError
import uuid
import json

User = get_user_model()


class PDFTemplate(models.Model):
    """
    Main PDF template model with metadata, configuration, and version control.
    """

    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        ACTIVE = 'active', _('Active')
        ARCHIVED = 'archived', _('Archived')
        DEPRECATED = 'deprecated', _('Deprecated')

    class PageSize(models.TextChoices):
        A4 = 'A4', _('A4 (210 × 297 mm)')
        A3 = 'A3', _('A3 (297 × 420 mm)')
        A5 = 'A5', _('A5 (148 × 210 mm)')
        LETTER = 'LETTER', _('Letter (8.5 × 11 in)')
        LEGAL = 'LEGAL', _('Legal (8.5 × 14 in)')
        TABLOID = 'TABLOID', _('Tabloid (11 × 17 in)')
        CUSTOM = 'CUSTOM', _('Custom Size')

    class Orientation(models.TextChoices):
        PORTRAIT = 'portrait', _('Portrait')
        LANDSCAPE = 'landscape', _('Landscape')

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, help_text=_("Template name"))
    slug = models.SlugField(max_length=220, unique=True, blank=True)
    description = models.TextField(blank=True, help_text=_("Template description"))

    # Ownership and Access
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='pdf_templates'
    )
    organization = models.CharField(
        max_length=100,
        blank=True,
        help_text=_("Organization or company name")
    )

    # Template Configuration
    page_size = models.CharField(
        max_length=20,
        choices=PageSize.choices,
        default=PageSize.A4
    )
    orientation = models.CharField(
        max_length=20,
        choices=Orientation.choices,
        default=Orientation.PORTRAIT
    )

    # Custom page dimensions (used when page_size is CUSTOM)
    custom_width = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(50), MaxValueValidator(2000)],
        help_text=_("Custom width in mm")
    )
    custom_height = models.FloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(50), MaxValueValidator(2000)],
        help_text=_("Custom height in mm")
    )

    # Margins (in mm)
    margin_top = models.FloatField(
        default=20.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    margin_bottom = models.FloatField(
        default=20.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    margin_left = models.FloatField(
        default=20.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    margin_right = models.FloatField(
        default=20.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # Template Inheritance
    parent_template = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='child_templates',
        help_text=_("Parent template for inheritance")
    )

    # Version Control
    version = models.CharField(max_length=20, default='1.0.0')
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT
    )

    # Metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Additional template metadata")
    )

    # Template Settings
    settings = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Template-specific settings and configurations")
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Usage Statistics
    usage_count = models.PositiveIntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = _('PDF Template')
        verbose_name_plural = _('PDF Templates')
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['status', 'created_by']),
            models.Index(fields=['slug']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.name} (v{self.version})"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)

        # Ensure slug uniqueness
        if PDFTemplate.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
            counter = 1
            original_slug = self.slug
            while PDFTemplate.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1

        super().save(*args, **kwargs)

    def clean(self):
        """Validate template configuration."""
        if self.page_size == self.PageSize.CUSTOM:
            if not self.custom_width or not self.custom_height:
                raise ValidationError(_("Custom width and height are required for custom page size."))

        # Prevent circular inheritance
        if self.parent_template:
            parent = self.parent_template
            while parent:
                if parent == self:
                    raise ValidationError(_("Circular template inheritance is not allowed."))
                parent = parent.parent_template

    def get_default_settings(self):
        """Return default template settings."""
        return {
            'font_family': 'Arial',
            'font_size': 12,
            'line_height': 1.2,
            'text_color': '#000000',
            'background_color': '#FFFFFF',
            'header_enabled': False,
            'footer_enabled': False,
            'watermark_enabled': False,
            'page_numbers': True,
            'compression_level': 6,
        }

    def get_page_dimensions(self):
        """Get page dimensions in mm."""
        if self.page_size == self.PageSize.CUSTOM:
            return self.custom_width, self.custom_height

        # Standard page sizes in mm
        sizes = {
            'A4': (210, 297),
            'A3': (297, 420),
            'A5': (148, 210),
            'LETTER': (215.9, 279.4),
            'LEGAL': (215.9, 355.6),
            'TABLOID': (279.4, 431.8),
        }

        width, height = sizes.get(self.page_size, (210, 297))
        if self.orientation == self.Orientation.LANDSCAPE:
            width, height = height, width

        return width, height

    def increment_usage(self):
        """Increment usage count and update last used timestamp."""
        from django.utils import timezone
        self.usage_count += 1
        self.last_used = timezone.now()
        self.save(update_fields=['usage_count', 'last_used'])


class PDFElement(models.Model):
    """
    Base model for all PDF elements with polymorphic design.
    """

    class ElementType(models.TextChoices):
        TEXT = 'text', _('Text Element')
        IMAGE = 'image', _('Image Element')
        TABLE = 'table', _('Table Element')
        CONTAINER = 'container', _('Layout Container')
        LINE = 'line', _('Line Element')
        SHAPE = 'shape', _('Shape Element')

    class PositionType(models.TextChoices):
        ABSOLUTE = 'absolute', _('Absolute Position')
        RELATIVE = 'relative', _('Relative Position')
        FLOW = 'flow', _('Flow Layout')

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(
        PDFTemplate,
        on_delete=models.CASCADE,
        related_name='elements'
    )
    name = models.CharField(max_length=100, help_text=_("Element name"))
    element_type = models.CharField(
        max_length=20,
        choices=ElementType.choices
    )

    # Hierarchy and Ordering
    parent_element = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='child_elements'
    )
    order = models.PositiveIntegerField(default=0)

    # Position and Size (in mm)
    position_type = models.CharField(
        max_length=20,
        choices=PositionType.choices,
        default=PositionType.ABSOLUTE
    )
    x = models.FloatField(default=0, help_text=_("X position in mm"))
    y = models.FloatField(default=0, help_text=_("Y position in mm"))
    width = models.FloatField(null=True, blank=True, help_text=_("Width in mm"))
    height = models.FloatField(null=True, blank=True, help_text=_("Height in mm"))

    # Styling
    style = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Element styling properties")
    )

    # Conditional Rendering
    condition = models.TextField(
        blank=True,
        help_text=_("Condition for rendering this element (Jinja2 expression)")
    )

    # Data Binding
    data_source = models.CharField(
        max_length=200,
        blank=True,
        help_text=_("Data source path for dynamic content")
    )

    # Visibility and State
    visible = models.BooleanField(default=True)
    locked = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('PDF Element')
        verbose_name_plural = _('PDF Elements')
        ordering = ['order', 'created_at']
        indexes = [
            models.Index(fields=['template', 'element_type']),
            models.Index(fields=['parent_element', 'order']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_element_type_display()})"

    def get_default_style(self):
        """Return default style properties for this element type."""
        base_style = {
            'border_width': 0,
            'border_color': '#000000',
            'border_style': 'solid',
            'background_color': 'transparent',
            'opacity': 1.0,
            'z_index': 0,
        }

        type_specific = {
            'text': {
                'font_family': 'Arial',
                'font_size': 12,
                'font_weight': 'normal',
                'font_style': 'normal',
                'text_color': '#000000',
                'text_align': 'left',
                'line_height': 1.2,
                'text_decoration': 'none',
            },
            'image': {
                'fit': 'contain',
                'alignment': 'center',
                'alt_text': '',
            },
            'table': {
                'border_collapse': 'separate',
                'cell_padding': 5,
                'cell_spacing': 0,
                'header_background': '#f0f0f0',
                'stripe_rows': False,
            },
            'container': {
                'padding_top': 0,
                'padding_bottom': 0,
                'padding_left': 0,
                'padding_right': 0,
            },
        }

        base_style.update(type_specific.get(self.element_type, {}))
        return base_style


class TextElement(models.Model):
    """
    Text element with font, size, color, and positioning properties.
    """

    class TextAlign(models.TextChoices):
        LEFT = 'left', _('Left')
        CENTER = 'center', _('Center')
        RIGHT = 'right', _('Right')
        JUSTIFY = 'justify', _('Justify')

    class FontWeight(models.TextChoices):
        NORMAL = 'normal', _('Normal')
        BOLD = 'bold', _('Bold')
        LIGHT = 'light', _('Light')

    class FontStyle(models.TextChoices):
        NORMAL = 'normal', _('Normal')
        ITALIC = 'italic', _('Italic')
        OBLIQUE = 'oblique', _('Oblique')

    element = models.OneToOneField(
        PDFElement,
        on_delete=models.CASCADE,
        related_name='text_properties'
    )

    # Text Content
    content = models.TextField(help_text=_("Text content (supports Jinja2 templates)"))

    # Typography
    font_family = models.CharField(max_length=100, default='Arial')
    font_size = models.FloatField(
        default=12,
        validators=[MinValueValidator(6), MaxValueValidator(72)]
    )
    font_weight = models.CharField(
        max_length=20,
        choices=FontWeight.choices,
        default=FontWeight.NORMAL
    )
    font_style = models.CharField(
        max_length=20,
        choices=FontStyle.choices,
        default=FontStyle.NORMAL
    )

    # Text Formatting
    text_color = models.CharField(max_length=7, default='#000000')
    text_align = models.CharField(
        max_length=20,
        choices=TextAlign.choices,
        default=TextAlign.LEFT
    )
    line_height = models.FloatField(
        default=1.2,
        validators=[MinValueValidator(0.5), MaxValueValidator(3.0)]
    )

    # Text Decoration
    underline = models.BooleanField(default=False)
    strikethrough = models.BooleanField(default=False)

    # Advanced Features
    auto_resize = models.BooleanField(default=False)
    max_lines = models.PositiveIntegerField(null=True, blank=True)
    word_wrap = models.BooleanField(default=True)

    class Meta:
        verbose_name = _('Text Element')
        verbose_name_plural = _('Text Elements')

    def __str__(self):
        return f"Text: {self.content[:50]}..."


class ImageElement(models.Model):
    """
    Image element with scaling, positioning, and alt text properties.
    """

    class FitMode(models.TextChoices):
        CONTAIN = 'contain', _('Contain')
        COVER = 'cover', _('Cover')
        FILL = 'fill', _('Fill')
        SCALE_DOWN = 'scale-down', _('Scale Down')
        NONE = 'none', _('None')

    class Alignment(models.TextChoices):
        TOP_LEFT = 'top-left', _('Top Left')
        TOP_CENTER = 'top-center', _('Top Center')
        TOP_RIGHT = 'top-right', _('Top Right')
        CENTER_LEFT = 'center-left', _('Center Left')
        CENTER = 'center', _('Center')
        CENTER_RIGHT = 'center-right', _('Center Right')
        BOTTOM_LEFT = 'bottom-left', _('Bottom Left')
        BOTTOM_CENTER = 'bottom-center', _('Bottom Center')
        BOTTOM_RIGHT = 'bottom-right', _('Bottom Right')

    element = models.OneToOneField(
        PDFElement,
        on_delete=models.CASCADE,
        related_name='image_properties'
    )

    # Image Source
    image_url = models.URLField(blank=True, help_text=_("Image URL"))
    image_file = models.ImageField(
        upload_to='pdf_templates/images/',
        blank=True,
        null=True,
        help_text=_("Uploaded image file")
    )
    image_data_source = models.CharField(
        max_length=200,
        blank=True,
        help_text=_("Dynamic image source path")
    )

    # Image Properties
    fit_mode = models.CharField(
        max_length=20,
        choices=FitMode.choices,
        default=FitMode.CONTAIN
    )
    alignment = models.CharField(
        max_length=20,
        choices=Alignment.choices,
        default=Alignment.CENTER
    )

    # Accessibility
    alt_text = models.CharField(
        max_length=200,
        blank=True,
        help_text=_("Alternative text for accessibility")
    )

    # Image Effects
    opacity = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )
    rotation = models.FloatField(
        default=0,
        validators=[MinValueValidator(-360), MaxValueValidator(360)],
        help_text=_("Rotation angle in degrees")
    )

    # Quality Settings
    quality = models.PositiveIntegerField(
        default=85,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text=_("Image quality (1-100)")
    )

    class Meta:
        verbose_name = _('Image Element')
        verbose_name_plural = _('Image Elements')

    def __str__(self):
        if self.image_file:
            return f"Image: {self.image_file.name}"
        elif self.image_url:
            return f"Image: {self.image_url}"
        else:
            return f"Image: {self.image_data_source or 'Dynamic'}"


class TableElement(models.Model):
    """
    Table element with column definitions, styling, and data binding.
    """

    class BorderStyle(models.TextChoices):
        NONE = 'none', _('None')
        SOLID = 'solid', _('Solid')
        DASHED = 'dashed', _('Dashed')
        DOTTED = 'dotted', _('Dotted')

    element = models.OneToOneField(
        PDFElement,
        on_delete=models.CASCADE,
        related_name='table_properties'
    )

    # Table Structure
    columns = models.JSONField(
        default=list,
        help_text=_("Column definitions with headers, widths, and data sources")
    )

    # Data Source
    data_source = models.CharField(
        max_length=200,
        help_text=_("Data source path for table rows")
    )

    # Table Styling
    header_background_color = models.CharField(max_length=7, default='#f0f0f0')
    header_text_color = models.CharField(max_length=7, default='#000000')
    row_background_color = models.CharField(max_length=7, default='#ffffff')
    alternate_row_color = models.CharField(max_length=7, blank=True)

    # Border Settings
    border_style = models.CharField(
        max_length=20,
        choices=BorderStyle.choices,
        default=BorderStyle.SOLID
    )
    border_width = models.FloatField(default=1.0)
    border_color = models.CharField(max_length=7, default='#000000')

    # Cell Settings
    cell_padding = models.FloatField(default=5.0)
    cell_spacing = models.FloatField(default=0.0)

    # Table Features
    show_header = models.BooleanField(default=True)
    stripe_rows = models.BooleanField(default=False)
    repeat_header = models.BooleanField(default=True)

    class Meta:
        verbose_name = _('Table Element')
        verbose_name_plural = _('Table Elements')

    def __str__(self):
        return f"Table: {len(self.columns)} columns"

    def get_default_columns(self):
        """Return default column structure."""
        return [
            {
                'name': 'column1',
                'header': 'Column 1',
                'width': 100,
                'data_source': 'data.column1',
                'align': 'left',
                'format': 'text'
            }
        ]


class ContainerElement(models.Model):
    """
    Layout container for grouping and positioning other elements.
    """

    class FlexDirection(models.TextChoices):
        ROW = 'row', _('Row')
        COLUMN = 'column', _('Column')
        ROW_REVERSE = 'row-reverse', _('Row Reverse')
        COLUMN_REVERSE = 'column-reverse', _('Column Reverse')

    class JustifyContent(models.TextChoices):
        FLEX_START = 'flex-start', _('Flex Start')
        FLEX_END = 'flex-end', _('Flex End')
        CENTER = 'center', _('Center')
        SPACE_BETWEEN = 'space-between', _('Space Between')
        SPACE_AROUND = 'space-around', _('Space Around')
        SPACE_EVENLY = 'space-evenly', _('Space Evenly')

    class AlignItems(models.TextChoices):
        FLEX_START = 'flex-start', _('Flex Start')
        FLEX_END = 'flex-end', _('Flex End')
        CENTER = 'center', _('Center')
        STRETCH = 'stretch', _('Stretch')
        BASELINE = 'baseline', _('Baseline')

    element = models.OneToOneField(
        PDFElement,
        on_delete=models.CASCADE,
        related_name='container_properties'
    )

    # Layout Properties
    flex_direction = models.CharField(
        max_length=20,
        choices=FlexDirection.choices,
        default=FlexDirection.COLUMN
    )
    justify_content = models.CharField(
        max_length=20,
        choices=JustifyContent.choices,
        default=JustifyContent.FLEX_START
    )
    align_items = models.CharField(
        max_length=20,
        choices=AlignItems.choices,
        default=AlignItems.STRETCH
    )

    # Spacing
    gap = models.FloatField(default=0, help_text=_("Gap between child elements in mm"))

    # Padding
    padding_top = models.FloatField(default=0)
    padding_bottom = models.FloatField(default=0)
    padding_left = models.FloatField(default=0)
    padding_right = models.FloatField(default=0)

    # Background
    background_color = models.CharField(max_length=7, blank=True)
    background_image = models.ImageField(
        upload_to='pdf_templates/backgrounds/',
        blank=True,
        null=True
    )

    # Border Radius
    border_radius = models.FloatField(default=0)

    class Meta:
        verbose_name = _('Container Element')
        verbose_name_plural = _('Container Elements')

    def __str__(self):
        return f"Container: {self.get_flex_direction_display()}"


class TemplatePreview(models.Model):
    """
    Template preview system with sample data injection and real-time generation.
    """

    class PreviewStatus(models.TextChoices):
        PENDING = 'pending', _('Pending')
        GENERATING = 'generating', _('Generating')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(
        PDFTemplate,
        on_delete=models.CASCADE,
        related_name='previews'
    )

    # Preview Configuration
    sample_data = models.JSONField(
        default=dict,
        help_text=_("Sample data for preview generation")
    )

    # Preview Files
    preview_image = models.ImageField(
        upload_to='pdf_templates/previews/',
        blank=True,
        null=True,
        help_text=_("Preview image (PNG/JPEG)")
    )
    preview_pdf = models.FileField(
        upload_to='pdf_templates/previews/',
        blank=True,
        null=True,
        help_text=_("Preview PDF file")
    )

    # Generation Status
    status = models.CharField(
        max_length=20,
        choices=PreviewStatus.choices,
        default=PreviewStatus.PENDING
    )
    error_message = models.TextField(blank=True)

    # Metadata
    generated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='generated_previews'
    )
    generation_time = models.FloatField(null=True, blank=True, help_text=_("Generation time in seconds"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Template Preview')
        verbose_name_plural = _('Template Previews')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['template', 'status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Preview for {self.template.name} - {self.get_status_display()}"


class TemplateVersion(models.Model):
    """
    Template version control system for tracking changes and rollbacks.
    """

    class ChangeType(models.TextChoices):
        CREATED = 'created', _('Created')
        UPDATED = 'updated', _('Updated')
        PUBLISHED = 'published', _('Published')
        ARCHIVED = 'archived', _('Archived')
        RESTORED = 'restored', _('Restored')

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(
        PDFTemplate,
        on_delete=models.CASCADE,
        related_name='versions'
    )

    # Version Information
    version_number = models.CharField(max_length=20)
    change_type = models.CharField(
        max_length=20,
        choices=ChangeType.choices
    )
    change_description = models.TextField(blank=True)

    # Template Snapshot
    template_data = models.JSONField(
        help_text=_("Complete template configuration snapshot")
    )
    elements_data = models.JSONField(
        help_text=_("All template elements snapshot")
    )

    # Version Metadata
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='template_versions'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    # Rollback Information
    is_current = models.BooleanField(default=False)
    rollback_from = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='rollbacks'
    )

    class Meta:
        verbose_name = _('Template Version')
        verbose_name_plural = _('Template Versions')
        ordering = ['-created_at']
        unique_together = ['template', 'version_number']
        indexes = [
            models.Index(fields=['template', 'is_current']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.template.name} v{self.version_number}"

    def save(self, *args, **kwargs):
        # Ensure only one current version per template
        if self.is_current:
            TemplateVersion.objects.filter(
                template=self.template,
                is_current=True
            ).exclude(pk=self.pk).update(is_current=False)

        super().save(*args, **kwargs)


class GeneratedCode(models.Model):
    """
    Store generated code packages for different programming languages.
    """

    class Language(models.TextChoices):
        PYTHON = 'python', _('Python')
        JAVASCRIPT = 'javascript', _('JavaScript')
        PHP = 'php', _('PHP')
        JAVA = 'java', _('Java')
        CSHARP = 'csharp', _('C#')

    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        GENERATING = 'generating', _('Generating')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    template = models.ForeignKey(
        PDFTemplate,
        on_delete=models.CASCADE,
        related_name='generated_code'
    )

    # Code Generation Configuration
    language = models.CharField(
        max_length=20,
        choices=Language.choices
    )
    framework_options = models.JSONField(
        default=dict,
        help_text=_("Framework-specific options and configurations")
    )

    # Generated Files
    code_package = models.FileField(
        upload_to='generated_code/',
        blank=True,
        null=True,
        help_text=_("Generated code package (ZIP file)")
    )

    # Generation Status
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    error_message = models.TextField(blank=True)

    # Metadata
    generated_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='generated_code_packages'
    )
    generation_time = models.FloatField(null=True, blank=True)
    download_count = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = _('Generated Code')
        verbose_name_plural = _('Generated Code Packages')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['template', 'language']),
            models.Index(fields=['status']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.template.name} - {self.get_language_display()}"

    def increment_download_count(self):
        """Increment download count."""
        self.download_count += 1
        self.save(update_fields=['download_count'])
