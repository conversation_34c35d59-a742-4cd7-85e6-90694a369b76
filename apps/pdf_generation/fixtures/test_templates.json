[{"model": "pdf_generation.pdftemplate", "pk": "550e8400-e29b-41d4-a716-446655440001", "fields": {"name": "Invoice Template", "slug": "invoice-template", "description": "Professional invoice template with company branding", "status": "active", "version": "1.0.0", "page_size": "A4", "orientation": "portrait", "custom_width": null, "custom_height": null, "margin_top": 20.0, "margin_bottom": 20.0, "margin_left": 15.0, "margin_right": 15.0, "parent_template": null, "metadata": {"category": "business", "tags": ["invoice", "billing", "business"], "author": "PDFlex Team"}, "settings": {"auto_page_break": true, "compress": true, "font_subset": true}, "usage_count": 0, "last_used": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdftemplate", "pk": "550e8400-e29b-41d4-a716-446655440002", "fields": {"name": "Report Template", "slug": "report-template", "description": "Comprehensive report template with charts and tables", "status": "active", "version": "2.1.0", "page_size": "A4", "orientation": "portrait", "custom_width": null, "custom_height": null, "margin_top": 25.0, "margin_bottom": 25.0, "margin_left": 20.0, "margin_right": 20.0, "parent_template": null, "metadata": {"category": "reports", "tags": ["report", "analytics", "data"], "author": "PDFlex Team"}, "settings": {"auto_page_break": true, "compress": false, "font_subset": false}, "usage_count": 15, "last_used": "2024-01-15T10:30:00Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-15T10:30:00Z"}}, {"model": "pdf_generation.pdftemplate", "pk": "550e8400-e29b-41d4-a716-446655440003", "fields": {"name": "Certificate Template", "slug": "certificate-template", "description": "Elegant certificate template for awards and achievements", "status": "draft", "version": "1.0.0", "page_size": "A4", "orientation": "landscape", "custom_width": null, "custom_height": null, "margin_top": 30.0, "margin_bottom": 30.0, "margin_left": 40.0, "margin_right": 40.0, "parent_template": null, "metadata": {"category": "certificates", "tags": ["certificate", "award", "achievement"], "author": "PDFlex Team"}, "settings": {"auto_page_break": false, "compress": true, "font_subset": true}, "usage_count": 0, "last_used": null, "created_at": "2024-01-10T00:00:00Z", "updated_at": "2024-01-10T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440101", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Company Logo", "element_type": "image", "position_type": "absolute", "x": 15.0, "y": 15.0, "width": 50.0, "height": 25.0, "style": {"border": "none", "background": "transparent"}, "condition": null, "data_source": null, "visible": true, "locked": false, "order": 1, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440102", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Invoice Title", "element_type": "text", "position_type": "absolute", "x": 15.0, "y": 50.0, "width": 180.0, "height": 20.0, "style": {"text_align": "left", "font_weight": "bold"}, "condition": null, "data_source": null, "visible": true, "locked": false, "order": 2, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440103", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Customer Information", "element_type": "text", "position_type": "absolute", "x": 15.0, "y": 80.0, "width": 90.0, "height": 40.0, "style": {"text_align": "left", "line_height": 1.2}, "condition": null, "data_source": "customer", "visible": true, "locked": false, "order": 3, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440104", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Invoice Details", "element_type": "text", "position_type": "absolute", "x": 110.0, "y": 80.0, "width": 85.0, "height": 40.0, "style": {"text_align": "right", "line_height": 1.2}, "condition": null, "data_source": null, "visible": true, "locked": false, "order": 4, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440105", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Items Table", "element_type": "table", "position_type": "absolute", "x": 15.0, "y": 130.0, "width": 180.0, "height": 100.0, "style": {"border": "1px solid #ddd", "border_collapse": "collapse"}, "condition": null, "data_source": "items", "visible": true, "locked": false, "order": 5, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.pdfelement", "pk": "550e8400-e29b-41d4-a716-446655440106", "fields": {"template": "550e8400-e29b-41d4-a716-446655440001", "name": "Total Amount", "element_type": "text", "position_type": "absolute", "x": 130.0, "y": 240.0, "width": 65.0, "height": 15.0, "style": {"text_align": "right", "font_weight": "bold", "font_size": "14px"}, "condition": null, "data_source": null, "visible": true, "locked": false, "order": 6, "parent_element": null, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}}, {"model": "pdf_generation.textelement", "pk": 1, "fields": {"element": "550e8400-e29b-41d4-a716-446655440102", "content": "INVOICE #{{invoice_number}}", "font_family": "<PERSON><PERSON>", "font_size": 18.0, "font_weight": "bold", "font_style": "normal", "text_color": "#000000", "text_align": "left", "line_height": 1.0, "underline": false, "strikethrough": false, "auto_resize": false, "max_lines": null, "word_wrap": true}}, {"model": "pdf_generation.textelement", "pk": 2, "fields": {"element": "550e8400-e29b-41d4-a716-446655440103", "content": "Bill To:\n{{customer.name}}\n{{customer.address}}\n{{customer.city}}, {{customer.state}} {{customer.zip}}", "font_family": "<PERSON><PERSON>", "font_size": 10.0, "font_weight": "normal", "font_style": "normal", "text_color": "#000000", "text_align": "left", "line_height": 1.2, "underline": false, "strikethrough": false, "auto_resize": false, "max_lines": null, "word_wrap": true}}, {"model": "pdf_generation.textelement", "pk": 3, "fields": {"element": "550e8400-e29b-41d4-a716-446655440104", "content": "Invoice Date: {{invoice_date}}\nDue Date: {{due_date}}\nInvoice #: {{invoice_number}}", "font_family": "<PERSON><PERSON>", "font_size": 10.0, "font_weight": "normal", "font_style": "normal", "text_color": "#000000", "text_align": "right", "line_height": 1.2, "underline": false, "strikethrough": false, "auto_resize": false, "max_lines": null, "word_wrap": true}}, {"model": "pdf_generation.textelement", "pk": 4, "fields": {"element": "550e8400-e29b-41d4-a716-446655440106", "content": "Total: ${{total|format_currency}}", "font_family": "<PERSON><PERSON>", "font_size": 14.0, "font_weight": "bold", "font_style": "normal", "text_color": "#000000", "text_align": "right", "line_height": 1.0, "underline": false, "strikethrough": false, "auto_resize": false, "max_lines": null, "word_wrap": false}}, {"model": "pdf_generation.imageelement", "pk": 1, "fields": {"element": "550e8400-e29b-41d4-a716-446655440101", "image_url": null, "image_file": null, "image_data_source": "company.logo_url", "fit_mode": "contain", "alignment": "left", "alt_text": "Company Logo", "opacity": 1.0, "rotation": 0.0, "quality": 85}}, {"model": "pdf_generation.tableelement", "pk": 1, "fields": {"element": "550e8400-e29b-41d4-a716-446655440105", "columns": [{"name": "description", "header": "Description", "width": 100, "data_source": "description", "align": "left"}, {"name": "quantity", "header": "Qty", "width": 25, "data_source": "quantity", "align": "center"}, {"name": "unit_price", "header": "Unit Price", "width": 30, "data_source": "unit_price", "align": "right", "format": "currency"}, {"name": "total", "header": "Total", "width": 25, "data_source": "total", "align": "right", "format": "currency"}], "data_source": "items", "header_background_color": "#f8f9fa", "header_text_color": "#000000", "row_background_color": "#ffffff", "alternate_row_color": "#f8f9fa", "border_style": "solid", "border_width": 1.0, "border_color": "#dee2e6", "cell_padding": 5.0, "cell_spacing": 0.0, "show_header": true, "stripe_rows": true, "repeat_header": false}}]