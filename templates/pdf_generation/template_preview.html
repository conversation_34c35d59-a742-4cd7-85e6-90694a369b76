{% extends "base.html" %}
{% load static %}

{% block title %}Preview Template - {{ template.name }}{% endblock %}

{% block extra_css %}
<style>
    .preview-container {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 20px;
        height: calc(100vh - 120px);
    }
    
    .preview-canvas {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        position: relative;
        overflow: auto;
    }
    
    .preview-sidebar {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        overflow-y: auto;
    }
    
    .template-element {
        position: absolute;
        border: 2px dashed #007cba;
        background: rgba(0, 124, 186, 0.1);
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .template-element:hover {
        border-color: #0056b3;
        background: rgba(0, 124, 186, 0.2);
    }
    
    .template-element.selected {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
    }
    
    .element-label {
        position: absolute;
        top: -20px;
        left: 0;
        background: #007cba;
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
    }
    
    .data-editor {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .preview-controls {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-pending { background-color: #ffc107; }
    .status-generating { background-color: #17a2b8; }
    .status-completed { background-color: #28a745; }
    .status-failed { background-color: #dc3545; }
    
    .loading-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007cba;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <h1>Preview: {{ template.name }}</h1>
            <p class="text-muted">{{ template.description }}</p>
        </div>
        <div class="col-auto">
            <a href="{% url 'pdf_generation:template_detail' template.pk %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Template
            </a>
        </div>
    </div>
    
    <div class="preview-container">
        <!-- Preview Canvas -->
        <div class="preview-canvas" id="previewCanvas">
            <div class="preview-page" id="previewPage" style="width: 210mm; height: 297mm; background: white; margin: 20px auto; position: relative; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                {% for element in elements %}
                <div class="template-element" 
                     data-element-id="{{ element.id }}"
                     data-element-type="{{ element.element_type }}"
                     style="left: {{ element.x }}mm; top: {{ element.y }}mm; width: {{ element.width|default:50 }}mm; height: {{ element.height|default:20 }}mm;">
                    <div class="element-label">{{ element.name }}</div>
                    {% if element.element_type == 'text' %}
                        <div style="padding: 2px; font-size: 10px; overflow: hidden;">
                            {{ element.text_properties.content|default:"Text content" }}
                        </div>
                    {% elif element.element_type == 'image' %}
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-image"></i> Image
                        </div>
                    {% elif element.element_type == 'table' %}
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <i class="fas fa-table"></i> Table
                        </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Preview Sidebar -->
        <div class="preview-sidebar">
            <div class="preview-controls">
                <h5>Preview Controls</h5>
                <button id="generatePreview" class="btn btn-primary btn-sm">
                    <i class="fas fa-play"></i> Generate Preview
                </button>
                <button id="downloadPreview" class="btn btn-success btn-sm" style="display: none;">
                    <i class="fas fa-download"></i> Download PDF
                </button>
                
                <div id="previewStatus" class="mt-2" style="display: none;">
                    <span class="status-indicator status-pending"></span>
                    <span id="statusText">Ready</span>
                </div>
            </div>
            
            <div class="mb-4">
                <h6>Sample Data</h6>
                <p class="small text-muted">Edit the sample data below to customize the preview:</p>
                <textarea id="sampleDataEditor" class="form-control data-editor" rows="15">{{ sample_data }}</textarea>
                <button id="updateData" class="btn btn-outline-primary btn-sm mt-2">
                    <i class="fas fa-sync"></i> Update Preview Data
                </button>
            </div>
            
            <div id="elementDetails" style="display: none;">
                <h6>Element Details</h6>
                <div id="elementInfo"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class TemplatePreview {
    constructor() {
        this.templateId = '{{ template.id }}';
        this.currentPreviewId = null;
        this.selectedElement = null;
        this.pollInterval = null;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Generate preview button
        document.getElementById('generatePreview').addEventListener('click', () => {
            this.generatePreview();
        });
        
        // Update data button
        document.getElementById('updateData').addEventListener('click', () => {
            this.updatePreviewData();
        });
        
        // Element selection
        document.querySelectorAll('.template-element').forEach(element => {
            element.addEventListener('click', (e) => {
                this.selectElement(e.currentTarget);
            });
        });
        
        // Download preview button
        document.getElementById('downloadPreview').addEventListener('click', () => {
            this.downloadPreview();
        });
    }
    
    generatePreview() {
        const sampleData = this.getSampleData();
        if (!sampleData) return;
        
        this.updateStatus('generating', 'Generating preview...');
        
        fetch(`/pdf-generation/api/template/${this.templateId}/generate-preview/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify({
                sample_data: sampleData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.pollPreviewStatus(data.task_id);
            } else {
                this.updateStatus('failed', 'Failed to start preview generation');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.updateStatus('failed', 'Error starting preview generation');
        });
    }
    
    pollPreviewStatus(taskId) {
        // In a real implementation, you'd poll the task status
        // For now, we'll simulate the process
        setTimeout(() => {
            this.updateStatus('completed', 'Preview generated successfully');
            document.getElementById('downloadPreview').style.display = 'inline-block';
        }, 3000);
    }
    
    updatePreviewData() {
        const sampleData = this.getSampleData();
        if (sampleData) {
            // Update the preview canvas with new data
            this.updateStatus('pending', 'Data updated - click Generate Preview to see changes');
            document.getElementById('downloadPreview').style.display = 'none';
        }
    }
    
    selectElement(element) {
        // Remove previous selection
        document.querySelectorAll('.template-element.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select new element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Show element details
        this.showElementDetails(element);
    }
    
    showElementDetails(element) {
        const elementId = element.dataset.elementId;
        const elementType = element.dataset.elementType;
        const elementName = element.querySelector('.element-label').textContent;
        
        const detailsDiv = document.getElementById('elementDetails');
        const infoDiv = document.getElementById('elementInfo');
        
        infoDiv.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">${elementName}</h6>
                    <p class="card-text">
                        <strong>Type:</strong> ${elementType}<br>
                        <strong>ID:</strong> ${elementId}<br>
                        <strong>Position:</strong> ${element.style.left}, ${element.style.top}<br>
                        <strong>Size:</strong> ${element.style.width} × ${element.style.height}
                    </p>
                </div>
            </div>
        `;
        
        detailsDiv.style.display = 'block';
    }
    
    getSampleData() {
        try {
            const dataText = document.getElementById('sampleDataEditor').value;
            return JSON.parse(dataText);
        } catch (error) {
            alert('Invalid JSON in sample data. Please check your syntax.');
            return null;
        }
    }
    
    updateStatus(status, message) {
        const statusDiv = document.getElementById('previewStatus');
        const statusIndicator = statusDiv.querySelector('.status-indicator');
        const statusText = document.getElementById('statusText');
        
        statusIndicator.className = `status-indicator status-${status}`;
        statusText.textContent = message;
        statusDiv.style.display = 'block';
        
        if (status === 'generating') {
            statusText.innerHTML = `<span class="loading-spinner"></span> ${message}`;
        }
    }
    
    downloadPreview() {
        if (this.currentPreviewId) {
            window.location.href = `/pdf-generation/download/preview/${this.currentPreviewId}/`;
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }
}

// Initialize preview when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TemplatePreview();
});
</script>
{% endblock %}
