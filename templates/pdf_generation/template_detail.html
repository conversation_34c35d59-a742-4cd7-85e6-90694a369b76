{% extends "base.html" %}
{% load static %}

{% block title %}{{ template.name }} - Template Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'pdf_generation:template_list' %}">Templates</a>
                    </li>
                    <li class="breadcrumb-item active">{{ template.name }}</li>
                </ol>
            </nav>
            <h1>{{ template.name }}</h1>
            <p class="text-muted">{{ template.description }}</p>
        </div>
        <div class="col-auto">
            <div class="btn-group" role="group">
                <a href="{% url 'pdf_generation:template_preview' template.pk %}" 
                   class="btn btn-success">
                    <i class="fas fa-play"></i> Preview
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle" 
                        data-bs-toggle="dropdown">
                    <i class="fas fa-code"></i> Generate Code
                </button>
                <ul class="dropdown-menu">
                    {% for lang in supported_languages %}
                    <li><a class="dropdown-item" href="#" onclick="generateCode('{{ template.pk }}', '{{ lang }}')">
                        {% if lang == 'python' %}<i class="fab fa-python"></i> Python
                        {% elif lang == 'javascript' %}<i class="fab fa-js"></i> JavaScript
                        {% elif lang == 'php' %}<i class="fab fa-php"></i> PHP
                        {% endif %}
                    </a></li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Template Info Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">{{ template.elements.count }}</h5>
                    <p class="card-text text-muted">Elements</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">{{ template.usage_count }}</h5>
                    <p class="card-text text-muted">Times Used</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">{{ generated_codes|length }}</h5>
                    <p class="card-text text-muted">Generated Codes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">{{ previews|length }}</h5>
                    <p class="card-text text-muted">Previews</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content Tabs -->
    <ul class="nav nav-tabs" id="templateTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" 
                    data-bs-target="#overview" type="button" role="tab">
                <i class="fas fa-info-circle"></i> Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="elements-tab" data-bs-toggle="tab" 
                    data-bs-target="#elements" type="button" role="tab">
                <i class="fas fa-puzzle-piece"></i> Elements
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="generated-tab" data-bs-toggle="tab" 
                    data-bs-target="#generated" type="button" role="tab">
                <i class="fas fa-code"></i> Generated Code
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="previews-tab" data-bs-toggle="tab" 
                    data-bs-target="#previews" type="button" role="tab">
                <i class="fas fa-eye"></i> Previews
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="versions-tab" data-bs-toggle="tab" 
                    data-bs-target="#versions" type="button" role="tab">
                <i class="fas fa-history"></i> Versions
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="templateTabsContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Template Configuration</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-{% if template.status == 'active' %}success{% elif template.status == 'draft' %}warning{% else %}secondary{% endif %}">
                                        {{ template.get_status_display }}
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-4">Page Size:</dt>
                                <dd class="col-sm-8">{{ template.get_page_size_display }}</dd>
                                
                                <dt class="col-sm-4">Orientation:</dt>
                                <dd class="col-sm-8">{{ template.get_orientation_display }}</dd>
                                
                                <dt class="col-sm-4">Version:</dt>
                                <dd class="col-sm-8">{{ template.version }}</dd>
                                
                                <dt class="col-sm-4">Created:</dt>
                                <dd class="col-sm-8">{{ template.created_at|date:"M d, Y" }}</dd>
                                
                                <dt class="col-sm-4">Last Updated:</dt>
                                <dd class="col-sm-8">{{ template.updated_at|date:"M d, Y H:i" }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Page Dimensions</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-6">Width:</dt>
                                <dd class="col-sm-6">{{ template.custom_width|default:template.page_size }} mm</dd>
                                
                                <dt class="col-sm-6">Height:</dt>
                                <dd class="col-sm-6">{{ template.custom_height|default:template.page_size }} mm</dd>
                                
                                <dt class="col-sm-6">Top Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_top }} mm</dd>
                                
                                <dt class="col-sm-6">Bottom Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_bottom }} mm</dd>
                                
                                <dt class="col-sm-6">Left Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_left }} mm</dd>
                                
                                <dt class="col-sm-6">Right Margin:</dt>
                                <dd class="col-sm-6">{{ template.margin_right }} mm</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Elements Tab -->
        <div class="tab-pane fade" id="elements" role="tabpanel">
            <div class="mt-4">
                {% if template.elements.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Position</th>
                                <th>Size</th>
                                <th>Visible</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for element in template.elements.all %}
                            <tr>
                                <td>{{ element.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ element.get_element_type_display }}</span>
                                </td>
                                <td>{{ element.x }}, {{ element.y }} mm</td>
                                <td>{{ element.width|default:"auto" }} × {{ element.height|default:"auto" }} mm</td>
                                <td>
                                    {% if element.visible %}
                                        <i class="fas fa-eye text-success"></i>
                                    {% else %}
                                        <i class="fas fa-eye-slash text-muted"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="showElementDetails('{{ element.id }}')">
                                        <i class="fas fa-info"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-puzzle-piece fa-3x text-muted mb-3"></i>
                    <h5>No Elements</h5>
                    <p class="text-muted">This template doesn't have any elements yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Generated Code Tab -->
        <div class="tab-pane fade" id="generated" role="tabpanel">
            <div class="mt-4">
                {% if generated_codes %}
                <div class="row">
                    {% for code in generated_codes %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">
                                            {% if code.language == 'python' %}<i class="fab fa-python"></i>
                                            {% elif code.language == 'javascript' %}<i class="fab fa-js"></i>
                                            {% elif code.language == 'php' %}<i class="fab fa-php"></i>
                                            {% endif %}
                                            {{ code.get_language_display }}
                                        </h6>
                                        <p class="card-text small text-muted">
                                            Generated {{ code.created_at|timesince }} ago
                                        </p>
                                    </div>
                                    <span class="badge bg-{% if code.status == 'completed' %}success{% elif code.status == 'failed' %}danger{% elif code.status == 'generating' %}warning{% else %}secondary{% endif %}">
                                        {{ code.get_status_display }}
                                    </span>
                                </div>
                                
                                {% if code.status == 'completed' %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        Downloads: {{ code.download_count }} | 
                                        Size: {{ code.code_package.size|filesizeformat }}
                                    </small>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'pdf_generation:download_code' code.id %}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download"></i> Download
                                    </a>
                                </div>
                                {% elif code.status == 'failed' %}
                                <div class="mt-2">
                                    <small class="text-danger">{{ code.error_message }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-code fa-3x text-muted mb-3"></i>
                    <h5>No Generated Code</h5>
                    <p class="text-muted">Generate code packages for this template.</p>
                    <button class="btn btn-primary" data-bs-toggle="dropdown">
                        <i class="fas fa-plus"></i> Generate Code
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Previews Tab -->
        <div class="tab-pane fade" id="previews" role="tabpanel">
            <div class="mt-4">
                {% if previews %}
                <div class="row">
                    {% for preview in previews %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">Preview</h6>
                                        <p class="card-text small text-muted">
                                            {{ preview.created_at|date:"M d, Y H:i" }}
                                        </p>
                                    </div>
                                    <span class="badge bg-{% if preview.status == 'completed' %}success{% elif preview.status == 'failed' %}danger{% else %}warning{% endif %}">
                                        {{ preview.get_status_display }}
                                    </span>
                                </div>
                                
                                {% if preview.status == 'completed' and preview.preview_pdf %}
                                <div class="mt-2">
                                    <a href="{% url 'pdf_generation:download_preview' preview.id %}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download"></i> Download PDF
                                    </a>
                                </div>
                                {% elif preview.status == 'failed' %}
                                <div class="mt-2">
                                    <small class="text-danger">{{ preview.error_message }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-eye fa-3x text-muted mb-3"></i>
                    <h5>No Previews</h5>
                    <p class="text-muted">Generate a preview to see how your template looks.</p>
                    <a href="{% url 'pdf_generation:template_preview' template.pk %}" 
                       class="btn btn-success">
                        <i class="fas fa-play"></i> Generate Preview
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Versions Tab -->
        <div class="tab-pane fade" id="versions" role="tabpanel">
            <div class="mt-4">
                {% if versions %}
                <div class="timeline">
                    {% for version in versions %}
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>Version {{ version.version_number }}</h6>
                            <p class="text-muted">{{ version.get_change_type_display }}</p>
                            {% if version.change_description %}
                            <p>{{ version.change_description }}</p>
                            {% endif %}
                            <small class="text-muted">
                                {{ version.created_at|date:"M d, Y H:i" }} by {{ version.created_by.get_display_name }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5>No Version History</h5>
                    <p class="text-muted">Version history will appear here as you make changes.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007cba;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007cba;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function generateCode(templateId, language) {
    // Implementation similar to template_list.html
    console.log(`Generating ${language} code for template ${templateId}`);
}

function showElementDetails(elementId) {
    // Show element details modal or expand inline
    console.log(`Showing details for element ${elementId}`);
}
</script>
{% endblock %}
