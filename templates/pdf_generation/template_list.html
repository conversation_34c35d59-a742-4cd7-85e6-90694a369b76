{% extends "base.html" %}
{% load static %}

{% block title %}PDF Templates{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h1>PDF Templates</h1>
            <p class="text-muted">Manage your PDF generation templates</p>
        </div>
        <div class="col-auto">
            <a href="#" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Template
            </a>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex">
                <input type="text" name="search" class="form-control me-2" 
                       placeholder="Search templates..." value="{{ search }}">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-md-3">
            <form method="get">
                {% if search %}<input type="hidden" name="search" value="{{ search }}">{% endif %}
                <select name="status" class="form-select" onchange="this.form.submit()">
                    <option value="">All Status</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </form>
        </div>
    </div>
    
    <!-- Templates Grid -->
    {% if templates %}
    <div class="row">
        {% for template in templates %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ template.name }}</h6>
                    <span class="badge bg-{% if template.status == 'active' %}success{% elif template.status == 'draft' %}warning{% else %}secondary{% endif %}">
                        {{ template.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted small">{{ template.description|truncatewords:20 }}</p>
                    
                    <div class="row text-center small text-muted mb-3">
                        <div class="col-4">
                            <div><strong>{{ template.elements.count }}</strong></div>
                            <div>Elements</div>
                        </div>
                        <div class="col-4">
                            <div><strong>{{ template.usage_count }}</strong></div>
                            <div>Uses</div>
                        </div>
                        <div class="col-4">
                            <div><strong>{{ template.page_size }}</strong></div>
                            <div>Size</div>
                        </div>
                    </div>
                    
                    <div class="small text-muted mb-3">
                        <i class="fas fa-clock"></i> Updated {{ template.updated_at|timesince }} ago
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <a href="{% url 'pdf_generation:template_detail' template.pk %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{% url 'pdf_generation:template_preview' template.pk %}" 
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-play"></i> Preview
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-code"></i> Generate
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="generateCode('{{ template.pk }}', 'python')">
                                    <i class="fab fa-python"></i> Python
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="generateCode('{{ template.pk }}', 'javascript')">
                                    <i class="fab fa-js"></i> JavaScript
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="generateCode('{{ template.pk }}', 'php')">
                                    <i class="fab fa-php"></i> PHP
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Templates pagination">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
            </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
            </li>
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="fas fa-file-pdf fa-4x text-muted mb-3"></i>
        <h3>No Templates Found</h3>
        <p class="text-muted">
            {% if search or status_filter %}
                No templates match your search criteria. <a href="{% url 'pdf_generation:template_list' %}">Clear filters</a>
            {% else %}
                Get started by creating your first PDF template.
            {% endif %}
        </p>
        {% if not search and not status_filter %}
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Your First Template
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Code Generation Modal -->
<div class="modal fade" id="codeGenerationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Generate Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="generationStatus">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <span>Starting code generation...</span>
                    </div>
                </div>
                <div id="generationResult" style="display: none;">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Code generated successfully!
                    </div>
                    <button id="downloadCode" class="btn btn-success">
                        <i class="fas fa-download"></i> Download Code Package
                    </button>
                </div>
                <div id="generationError" style="display: none;">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <span id="errorMessage"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateCode(templateId, language) {
    const modal = new bootstrap.Modal(document.getElementById('codeGenerationModal'));
    modal.show();
    
    // Reset modal state
    document.getElementById('generationStatus').style.display = 'block';
    document.getElementById('generationResult').style.display = 'none';
    document.getElementById('generationError').style.display = 'none';
    
    // Start code generation
    fetch(`/pdf-generation/api/template/${templateId}/generate-code/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            language: language,
            options: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Poll for completion (simplified)
            setTimeout(() => {
                document.getElementById('generationStatus').style.display = 'none';
                document.getElementById('generationResult').style.display = 'block';
                
                // Set up download button (you'd get the actual generated code ID from the API)
                document.getElementById('downloadCode').onclick = () => {
                    // In a real implementation, you'd use the actual generated code ID
                    window.location.href = `/pdf-generation/download/code/${data.task_id}/`;
                };
            }, 3000);
        } else {
            showGenerationError(data.error || 'Unknown error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showGenerationError('Network error occurred');
    });
}

function showGenerationError(message) {
    document.getElementById('generationStatus').style.display = 'none';
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('generationError').style.display = 'block';
}
</script>
{% endblock %}
